<?xml version="1.0" encoding="UTF-8"?>
<!--
  Copyright © 2014 Cask Data, Inc.

  Licensed under the Apache License, Version 2.0 (the "License"); you may not
  use this file except in compliance with the License. You may obtain a copy of
  the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
  License for the specific language governing permissions and limitations under
  the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>tephra</artifactId>
    <groupId>co.cask.tephra</groupId>
    <version>0.6.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>tephra-hbase-compat-1.0</artifactId>
  <name>Tephra HBase 1.0 Compatibility</name>

  <properties>
    <!-- HBase 1.0 only supports Hadoop 2.4 or newer -->
    <hadoop.version>2.4.0</hadoop.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>co.cask.tephra</groupId>
      <artifactId>tephra-api</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>co.cask.tephra</groupId>
      <artifactId>tephra-core</artifactId>
      <version>${project.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.apache.hbase</groupId>
          <artifactId>hbase</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.hbase</groupId>
      <artifactId>hbase-common</artifactId>
      <version>${hbase10.version}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.hbase</groupId>
      <artifactId>hbase-client</artifactId>
      <version>${hbase10.version}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.hbase</groupId>
      <artifactId>hbase-protocol</artifactId>
      <version>${hbase10.version}</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.hbase</groupId>
      <artifactId>hbase-server</artifactId>
      <version>${hbase10.version}</version>
      <scope>provided</scope>
    </dependency>

    <!-- Test dependencies -->
    <dependency>
      <groupId>co.cask.tephra</groupId>
      <artifactId>tephra-core</artifactId>
      <version>${project.version}</version>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.hbase</groupId>
      <artifactId>hbase-server</artifactId>
      <version>${hbase10.version}</version>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.hbase</groupId>
      <artifactId>hbase-testing-util</artifactId>
      <version>${hbase10.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

</project>
