<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<packaging>pom</packaging>

	<groupId>cn.hutool</groupId>
	<artifactId>hutool-parent</artifactId>
	<version>5.7.15</version>
	<name>hutool</name>
	<description>Hutool是一个小而全的Java工具类库，通过静态方法封装，降低相关API的学习成本，提高工作效率，使Java拥有函数式语言般的优雅，让Java语言也可以“甜甜的”。</description>
	<url>https://github.com/dromara/hutool</url>

	<modules>
		<module>hutool-all</module>
		<module>hutool-bom</module>
		<module>hutool-aop</module>
		<module>hutool-bloomFilter</module>
		<module>hutool-cache</module>
		<module>hutool-core</module>
		<module>hutool-cron</module>
		<module>hutool-crypto</module>
		<module>hutool-db</module>
		<module>hutool-dfa</module>
		<module>hutool-extra</module>
		<module>hutool-http</module>
		<module>hutool-log</module>
		<module>hutool-script</module>
		<module>hutool-setting</module>
		<module>hutool-system</module>
		<module>hutool-json</module>
		<module>hutool-poi</module>
		<module>hutool-captcha</module>
		<module>hutool-socket</module>
		<module>hutool-jwt</module>
	</modules>

	<properties>
		<project.build.sourceEncoding>utf-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>utf-8</project.reporting.outputEncoding>

		<!-- versions -->
		<compile.version>8</compile.version>
		<junit.version>4.13.2</junit.version>
		<lombok.version>1.18.22</lombok.version>
	</properties>

	<dependencies>
		<!-- 全局单元测试 -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>${junit.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok.version}</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<issueManagement>
		<system>Github Issue</system>
		<url>https://github.com/dromara/hutool/issues</url>
	</issueManagement>

	<licenses>
		<license>
			<name>Mulan Permissive Software License，Version 2</name>
			<url>https://license.coscl.org.cn/MulanPSL2</url>
		</license>
	</licenses>

	<developers>
		<developer>
			<name>Looly</name>
			<email><EMAIL></email>
		</developer>
	</developers>

	<scm>
		<connection>scm:**************:dromara/hutool.git</connection>
		<developerConnection>scm:**************:dromara/hutool.git</developerConnection>
		<url>**************:dromara/hutool.git</url>
	</scm>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>${compile.version}</source>
					<target>${compile.version}</target>
				</configuration>
			</plugin>
			<!-- Javadoc -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>3.1.1</version>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>

		</plugins>
	</build>

	<profiles>
		<profile>
			<id>release</id>
			<distributionManagement>
				<snapshotRepository>
					<id>oss</id>
					<url>https://oss.sonatype.org/content/repositories/snapshots/</url>
				</snapshotRepository>
				<repository>
					<id>oss</id>
					<url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
				</repository>
			</distributionManagement>
			<build>
				<plugins>
					<!-- Source -->
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-source-plugin</artifactId>
						<version>3.2.1</version>
						<executions>
							<execution>
								<id>oss</id>
								<phase>package</phase>
								<goals>
									<goal>jar-no-fork</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<!-- 测试覆盖度 -->
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>cobertura-maven-plugin</artifactId>
						<version>2.7</version>
						<configuration>
							<formats>
								<format>html</format>
								<format>xml</format>
							</formats>
							<check />
						</configuration>
					</plugin>
					<!-- Gpg Signature -->
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-gpg-plugin</artifactId>
						<version>1.6</version>
						<executions>
							<execution>
								<id>oss</id>
								<phase>verify</phase>
								<goals>
									<goal>sign</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>org.sonatype.plugins</groupId>
						<artifactId>nexus-staging-maven-plugin</artifactId>
						<version>1.6.8</version>
						<extensions>true</extensions>
						<configuration>
							<serverId>oss</serverId>
							<nexusUrl>https://oss.sonatype.org/</nexusUrl>
							<autoReleaseAfterClose>true</autoReleaseAfterClose>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

</project>
