
<project xmlns='http://maven.apache.org/POM/4.0.0' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance' xsi:schemaLocation='http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd'>
  <modelVersion>
    4.0.0
  </modelVersion>
  <groupId>
    biz.aQute
  </groupId>
  <artifactId>
    bndlib
  </artifactId>
  <version>
    1.43.0
  </version>
  <description>
    A library for manipulating and generating bundles
  </description>
  <name>
    aQute Bundle Tool Library
  </name>
  <url>
    http://www.aQute.biz/Code/Bnd
  </url>
  <scm>
    <url>
      git://github.com/bnd/bnd.git
    </url>
    <connection>
      git://github.com/bnd/bnd.git
    </connection>
    <developerConnection>
      git://github.com/bnd/bnd.git
    </developerConnection>
  </scm>
  <organization>
    <name>
      aQute SARL
    </name>
    <url>
      http://www.aQute.biz
    </url>
  </organization>
  <developers>
    <developer>
      <id>
        <EMAIL>
      </id>
      <name>
        Peter.Kriens
      </name>
      <email>
        <EMAIL>
      </email>
      <organization>
        aQute
      </organization>
    </developer>
  </developers>
  <licenses>
    <license>
      <name>
        All files contained in this JAR are licensed under the Apache
         2.0 license, unless noted differently in their source (see
         swing2swt).
      </name>
      <url>
        http://www.opensource.org/licenses/apache2.0.php
      </url>
      <distribution>
        repo
      </distribution>
    </license>
  </licenses>
</project>