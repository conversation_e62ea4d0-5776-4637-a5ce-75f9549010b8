<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>biz.aQute.bnd</groupId>
  <artifactId>biz.aQute.bndlib</artifactId>
  <version>3.3.0</version>
  <description>A Swiss Army Knife for OSGi</description>
  <name>bndlib</name>
  <url>http://bnd.bndtools.org/</url>
  <organization>
    <name>Bndtools</name>
    <url>http://bndtools.org/</url>
  </organization>
  <licenses>
    <license>
      <name>Apache-2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0</url>
      <distribution>repo</distribution>
      <comments>Apache License, Version 2.0</comments>
    </license>
  </licenses>
  <scm>
    <url>https://github.com/bndtools/bnd</url>
    <connection>scm:git:https://github.com/bndtools/bnd.git</connection>
    <developerConnection>scm:git:**************:bndtools/bnd.git</developerConnection>
  </scm>
  <developers>
    <developer>
      <id><EMAIL></id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id><EMAIL></id>
      <email><EMAIL></email>
    </developer>
  </developers>
</project>
