<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>biz.aQute.bnd</groupId>
  <artifactId>biz.aQute.bndlib</artifactId>
  <version>6.2.0</version>
  <description>bndlib: A Swiss Army Knife for OSGi</description>
  <name>biz.aQute.bndlib</name>
  <url>https://bnd.bndtools.org/</url>
  <organization>
    <name>Bndtools</name>
    <url>https://bndtools.org/</url>
  </organization>
  <licenses>
    <license>
      <name>(Apache-2.0 OR EPL-2.0)</name>
      <url>https://opensource.org/licenses/Apache-2.0,https://opensource.org/licenses/EPL-2.0</url>
      <distribution>repo</distribution>
      <comments>This program and the accompanying materials are made available under the terms of the Apache License, Version 2.0, or the Eclipse Public License 2.0.</comments>
    </license>
  </licenses>
  <scm>
    <url>https://github.com/bndtools/bnd</url>
    <connection>scm:git:https://github.com/bndtools/bnd.git</connection>
    <developerConnection>scm:git:**************:bndtools/bnd.git</developerConnection>
    <tag>6.2.0</tag>
  </scm>
  <developers>
    <developer>
      <id>pkriens</id>
      <email><EMAIL></email>
      <name>Peter Kriens</name>
      <organization>Bndtools</organization>
      <organizationUrl>https://github.com/bndtools</organizationUrl>
      <roles>
        <role>architect</role>
        <role>developer</role>
      </roles>
      <timezone>1</timezone>
    </developer>
    <developer>
      <id>bjhargrave</id>
      <name>BJ Hargrave</name>
      <email><EMAIL></email>
      <url>https://github.com/bjhargrave</url>
      <organization>IBM</organization>
      <organizationUrl>https://developer.ibm.com</organizationUrl>
      <roles>
        <role>developer</role>
      </roles>
      <timezone>America/New_York</timezone>
    </developer>
    <developer>
      <id>rotty3000</id>
      <name>Ray Augé</name>
      <email><EMAIL></email>
      <url>https://rotty3000.github.io</url>
      <organization>Liferay Inc.</organization>
      <organizationUrl>https://www.liferay.com</organizationUrl>
      <roles>
        <role>developer</role>
      </roles>
      <timezone>America/New_York</timezone>
    </developer>
  </developers>
  <dependencies>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>osgi.annotation</artifactId>
      <version>8.1.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>org.osgi.dto</artifactId>
      <version>1.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>org.osgi.resource</artifactId>
      <version>1.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>org.osgi.framework</artifactId>
      <version>1.8.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>org.osgi.util.tracker</artifactId>
      <version>1.5.4</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>org.osgi.namespace.contract</artifactId>
      <version>1.0.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>org.osgi.namespace.extender</artifactId>
      <version>1.0.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>org.osgi.namespace.implementation</artifactId>
      <version>1.0.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>org.osgi.namespace.service</artifactId>
      <version>1.0.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>org.osgi.service.log</artifactId>
      <version>1.3.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>org.osgi.service.repository</artifactId>
      <version>1.1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>org.osgi.util.function</artifactId>
      <version>1.2.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>org.osgi.util.promise</artifactId>
      <version>1.2.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>biz.aQute.bnd</groupId>
      <artifactId>biz.aQute.bnd.util</artifactId>
      <version>6.2.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.25</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>
