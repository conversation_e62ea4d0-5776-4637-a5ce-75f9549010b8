<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>biz.aQute.bnd</groupId>
  <artifactId>biz.aQute.bndlib</artifactId>
  <version>4.2.0</version>
  <description>bndlib: A Swiss Army Knife for OSGi</description>
  <name>biz.aQute.bndlib</name>
  <url>https://bnd.bndtools.org/</url>
  <organization>
    <name>Bndtools</name>
    <url>https://bndtools.org/</url>
  </organization>
  <licenses>
    <license>
      <name>Apache-2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0</url>
      <distribution>repo</distribution>
      <comments>Apache License, Version 2.0</comments>
    </license>
  </licenses>
  <scm>
    <url>https://github.com/bndtools/bnd</url>
    <connection>scm:git:https://github.com/bndtools/bnd.git</connection>
    <developerConnection>scm:git:**************:bndtools/bnd.git</developerConnection>
    <tag>4.2.0.REL</tag>
  </scm>
  <developers>
    <developer>
      <id><EMAIL></id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id><EMAIL></id>
      <email><EMAIL></email>
    </developer>
  </developers>
  <dependencies>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>osgi.annotation</artifactId>
      <version>7.0.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>org.osgi.util.function</artifactId>
      <version>1.1.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>org.osgi.util.promise</artifactId>
      <version>1.1.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.25</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>
