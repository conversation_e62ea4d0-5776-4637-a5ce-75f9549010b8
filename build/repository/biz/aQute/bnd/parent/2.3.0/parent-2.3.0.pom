<?xml version="1.0" encoding="UTF-8"?>
<project xmlns='http://maven.apache.org/POM/4.0.0' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance' 
  xsi:schemaLocation='http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd'>
  <modelVersion>4.0.0</modelVersion>

  <groupId>biz.aQute.bnd</groupId>
  <artifactId>parent</artifactId>
  <version>2.3.0</version>

  <packaging>pom</packaging>

  <name>Parent project for bnd tool suite</name>
  <description>Project information for bnd.</description>
  <url>http://www.aQute.biz/Code/Bnd</url>

  <organization>
    <name>aQute SARL</name>
    <url>http://www.aQute.biz</url>
  </organization>

  <licenses>
    <license>
      <name>Apache Software License 2.0</name>
      <url>http://www.opensource.org/licenses/apache2.0.php</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <developers>
    <developer>
      <id>bnd</id>
      <name>Peter Kriens</name>
      <organization>aQute SARL</organization>
      <roles>
        <role>Primary Developer</role>
      </roles>
    </developer>
    <developer>
      <id>bjhargrave</id>
      <name>BJ Hargrave</name>
    </developer>
    <developer>
      <id>mcculls</id>
      <name>Stuart McCulloch</name>
    </developer>
    <developer>
      <id>njbartlett</id>
      <name>Neil Bartlett</name>
      <organization>Paremus</organization>
    </developer>
    <developer>
      <id>fhuberts</id>
      <name>Ferry Huberts</name>
      <organization>Pelagic</organization>
    </developer>
    <developer>
      <id>psoreide</id>
      <name>PK S&#248;reide</name>
      <organization>Comactivity AB</organization>
    </developer>
  </developers>
  <contributors>
    <contributor>
      <!--<id>derheld42</id>-->
      <name>Carter Smithhart</name>
    </contributor>
    <contributor>
      <!--<id>cchabanois</id>-->
      <name>Chabanois Cédric</name>
      <organization>Entropysoft</organization>
    </contributor>
    <contributor>
      <!--<id>paulbakker</id>-->
      <name>Paul Bakker</name>
      <organization>Luminis Technologies</organization>
    </contributor>
    <contributor>
      <!--<id>djencks</id>-->
      <name>David Jencks</name>
    </contributor>
    <contributor>
      <!--<id>rkrzewski</id>-->
      <name>Rafa&#322; Krzewski</name>
      <organization>Caltha - Krzewski, Mach, Potempski Sp. J.</organization>
    </contributor>
    <contributor>
      <!--<id>xfournet</id>-->
      <name>Xavier Fournet</name>
    </contributor>
    <contributor>
      <!--<id>crabbkw</id>-->
      <name>Casey Crabb</name>
    </contributor>
    <contributor>
      <!--<id>david.bosschaert</id>-->
      <name>David Bosschaert</name>
    </contributor>
    <contributor>
      <!--<id>harald.wellmann</id>-->
      <name>Harald Wellmann</name>
    </contributor>
    <contributor>
      <!--<id>janwillem.janssen</id>-->
      <name>Jan Willem Janssen</name>
    </contributor>
    <contributor>
      <!--<id>marcel.offermans</id>-->
      <name>Marcel Offermans</name>
    </contributor>
    <contributor>
      <!--<id>marian.grigoras</id>-->
      <name>Marian Grigoras</name>
    </contributor>
    <contributor>
      <!--<id>markuswolf</id>-->
      <name>Markus Wolf</name>
    </contributor>
    <contributor>
      <!--<id>nicolas.lalevee</id>-->
      <name>Nicolas Lalevée</name>
    </contributor>
    <contributor>
      <!--<id>pierre.labiausse</id>-->
      <name>Pierre Labiausse</name>
    </contributor>
    <contributor>
      <!--<id>raymond.auge</id>-->
      <name>Raymond Auge</name>
    </contributor>
    <contributor>
      <!--<id>timothyjward</id>-->
      <name>Tim Ward</name>
    </contributor>
    <contributor>
      <!--<id>bramk</id>-->
      <name>Bram de Kruijff</name>
    </contributor>
    <contributor>
      <!--<id>tangyong</id>-->
      <name>Tang Yong</name>
    </contributor>
  </contributors>

  <scm>
    <url>https://github.com/bndtools/bnd</url>
    <connection>scm:git:git://github.com/bndtools/bnd.git</connection>
    <developerConnection>scm:git:ssh://github.com/bndtools/bnd.git</developerConnection>
  </scm>

</project>
