<!-- Copyright 2010 Atlassian Licensed under the Apache License, Version 
    2.0 (the "License"); you may not use this file except in compliance with 
    the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 
    Unless required by applicable law or agreed to in writing, software distributed 
    under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES 
    OR CONDITIONS OF ANY KIND, either express or implied. See the License for 
    the specific language governing permissions and limitations under the License. -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.atlassian.pom</groupId>
        <artifactId>public-pom</artifactId>
        <version>3.0.39</version>
    </parent>

    <groupId>com.atlassian.fugue</groupId>
    <artifactId>fugue-parent</artifactId>
    <packaging>pom</packaging>
    <version>2.2.1</version>
    <name>Functional Guava Extensions Library</name>

    <licenses>
        <license>
            <name>Apache License 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <modules>
        <module>fugue</module>
        <module>fugue-scala</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <jdkLevel>1.6</jdkLevel>
        <clover.version>3.1.0</clover.version>
        <guava.osgi.version>16.0.1</guava.osgi.version>
        <slf4j-api.version>1.6.0</slf4j-api.version>
        <atlassian-util-concurrent.version>2.4.1</atlassian-util-concurrent.version>
        <mockito-core.version>1.8.4</mockito-core.version>
        <junit.version>4.11</junit.version>
        <hamcrest.version>1.3</hamcrest.version>
        <jsr305.version>2.0.1</jsr305.version>
    </properties>

    <scm>
        <connection>scm:git:*****************:atlassian/fugue.git</connection>
        <developerConnection>scm:git:*****************:atlassian/fugue.git</developerConnection>
        <url>https://bitbucket.org/atlassian/fugue</url>
      <tag>fugue-parent-2.2.1</tag>
  </scm>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-site-plugin</artifactId>
                <!-- needed for the distribution management below -->
                <version>3.3</version>
            </plugin>
        </plugins>
    </build>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j-api.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-nop</artifactId>
                <version>${slf4j-api.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                  <groupId>com.google.guava</groupId>
                  <artifactId>guava</artifactId>
                  <version>${guava.osgi.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <!-- findbugs is only useful for compile/analysis, not needed
                    at runtime -->
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>jsr305</artifactId>
                <version>${jsr305.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.atlassian.util.concurrent</groupId>
                <artifactId>atlassian-util-concurrent</artifactId>
                <version>${atlassian-util-concurrent.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- Test deps -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito-core.version}</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.hamcrest</groupId>
                        <artifactId>hamcrest-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-core</artifactId>
                <version>${hamcrest.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-library</artifactId>
                <version>${hamcrest.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
