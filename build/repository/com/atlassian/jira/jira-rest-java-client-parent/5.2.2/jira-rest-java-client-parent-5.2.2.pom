<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.atlassian.pom</groupId>
        <artifactId>public-pom</artifactId>
        <version>5.0.8</version>
    </parent>

    <groupId>com.atlassian.jira</groupId>
    <artifactId>jira-rest-java-client-parent</artifactId>
    <version>5.2.2</version>
    <packaging>pom</packaging>

    <name>JIRA REST Java Client</name>
    <description>Java library which allows to easily talk to any JIRA 7.0+ instance.</description>
    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <modules>
        <module>api</module>
        <module>app</module>
        <module>core</module>
        <module>test</module>
    </modules>

    <scm>
        <connection>scm:git:ssh://*****************/atlassian/jira-rest-java-client.git</connection>
        <developerConnection>scm:git:ssh://*****************/atlassian/jira-rest-java-client.git</developerConnection>
        <url>https://bitbucket.org/atlassian/jira-rest-java-client/</url>
        <tag>jira-rest-java-client-parent-5.2.2</tag>
    </scm>
    <issueManagement>
        <system>JIRA</system>
        <url>https://ecosystem.atlassian.net/browse/JRJC</url>
    </issueManagement>

    <properties>
        <platform.version>3.1.7</platform.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <jira.version>8.4.0</jira.version>
        <jira.data.version>${jira.version}</jira.data.version>
        <jira.test-lib.version>${jira.version}</jira.test-lib.version>
        <jersey.client.version>1.19</jersey.client.version>
        <joda.time.version>2.9.9</joda.time.version>
        <atlassian.httpclient.version>2.0.0</atlassian.httpclient.version>
        <httpclient.version>4.5.3</httpclient.version>
        <httpcore.version>4.4.8</httpcore.version>
        <amps.version>6.3.22-fc341f3d2</amps.version>
        <testkit.version>8.1.5</testkit.version>
        <atlassian.util.concurrent>4.0.1</atlassian.util.concurrent>
        <guava.version>26.0-jre</guava.version>
        <jackson.version>1.9.13-atlassian-4</jackson.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.atlassian.platform</groupId>
                <artifactId>platform</artifactId>
                <version>${platform.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.atlassian.platform</groupId>
                <artifactId>third-party</artifactId>
                <version>${platform.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>${httpcore.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore-nio</artifactId>
                <version>${httpcore.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient-cache</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>${httpclient.version}</version>
            </dependency>

            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda.time.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.jersey</groupId>
                <artifactId>jersey-client</artifactId>
                <version>${jersey.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.jersey</groupId>
                <artifactId>jersey-json</artifactId>
                <version>${jersey.client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-core-asl</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-mapper-asl</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-jaxrs</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-xc</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.httpclient</groupId>
                <artifactId>atlassian-httpclient-api</artifactId>
                <version>${atlassian.httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.httpclient</groupId>
                <artifactId>atlassian-httpclient-library</artifactId>
                <version>${atlassian.httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.5</version>
            </dependency>
            <dependency>
                <groupId>io.atlassian.util.concurrent</groupId>
                <artifactId>atlassian-util-concurrent</artifactId>
                <version>${atlassian.util.concurrent}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <excludePackageNames>com.atlassian.jira.rest.client.internal*</excludePackageNames>
                    <links>
                        <link>https://docs.atlassian.com/software/jira/docs/api/${jira.version}/</link>
                    </links>
                </configuration>
                <reportSets>
                    <reportSet>
                        <id>non-aggregate</id>
                        <reports>
                            <report>javadoc</report>
                        </reports>
                    </reportSet>
                    <reportSet>
                        <id>aggregate</id>
                        <inherited>false</inherited>
                        <reports>
                            <report>aggregate</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jxr-plugin</artifactId>
                <!-- TODO BUILDENG-14322 remove this jxr version, once the override in public-pom is gone -->
                <version>2.5</version>
                <reportSets>
                    <reportSet>
                        <id>non-aggregate</id>
                        <reports>
                            <report>jxr</report>
                        </reports>
                    </reportSet>
                    <reportSet>
                        <id>aggregate</id>
                        <inherited>false</inherited>
                        <reports>
                            <report>aggregate</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <!-- TODO BUILDENG-14322 remove whole maven-project-info-reports-plugin definition from here, once the versions are fixed in the base-pom -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>2.9</version>
            </plugin>
        </plugins>
    </reporting>

    <profiles>
        <profile>
            <id>release</id>
            <properties>
                <skipTests>true</skipTests>
            </properties>
        </profile>
        <profile>
            <id>skipSitePlugin</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-site-plugin</artifactId>
                        <configuration>
                            <skip>true</skip>
                            <skipDeploy>true</skipDeploy>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
