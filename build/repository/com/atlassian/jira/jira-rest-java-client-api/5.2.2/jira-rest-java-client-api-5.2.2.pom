<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.atlassian.jira</groupId>
        <artifactId>jira-rest-java-client-parent</artifactId>
        <version>5.2.2</version>
    </parent>

    <artifactId>jira-rest-java-client-api</artifactId>

    <name>JIRA REST Java Client - Public API</name>
    <description>The public API for JIRA REST Java Client</description>

    <dependencies>
        <dependency>
            <groupId>io.atlassian.util.concurrent</groupId>
            <artifactId>atlassian-util-concurrent</artifactId>
        </dependency>
        <dependency>
            <groupId>com.atlassian.httpclient</groupId>
            <artifactId>atlassian-httpclient-api</artifactId>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <additionalDependencies>
                        <additionalDependency>
                            <groupId>com.atlassian.jira</groupId>
                            <artifactId>jira-api</artifactId>
                            <version>${jira.version}</version>
                        </additionalDependency>
                    </additionalDependencies>
                    <links>
                        <link>https://docs.atlassian.com/software/jira/docs/api/${jira.version}/</link>
                    </links>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <additionalDependencies>
                        <additionalDependency>
                            <groupId>com.atlassian.jira</groupId>
                            <artifactId>jira-api</artifactId>
                            <version>${jira.version}</version>
                        </additionalDependency>
                    </additionalDependencies>
                    <links>
                        <link>https://docs.atlassian.com/software/jira/docs/api/${jira.version}/</link>
                    </links>
                </configuration>
            </plugin>
        </plugins>
    </reporting>
</project>
