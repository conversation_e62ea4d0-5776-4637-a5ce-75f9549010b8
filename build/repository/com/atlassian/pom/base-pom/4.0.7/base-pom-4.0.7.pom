<?xml version="1.0" ?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.atlassian.pom</groupId>
    <artifactId>base-pom</artifactId>
    <version>4.0.7</version>
    <packaging>pom</packaging>

    <name>Atlassian Base POM</name>
    <description>Base POM for Atlassian projects</description>
    <url>http://www.atlassian.com/</url>
    <licenses>
        <license>
            <name>Atlassian 3.0 End User License Agreement</name>
            <url>http://www.atlassian.com/end-user-agreement/</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
      <developer>
        <id>charlie</id>
        <name>Charlie</name>
        <email><EMAIL></email>
      </developer>
    </developers>
    <organization>
        <name>Atlassian</name>
        <url>http://www.atlassian.com/</url>
    </organization>

    <prerequisites>
        <maven>3.0.5</maven>
    </prerequisites>

    <scm>
        <connection>scm:git:ssh://*****************/atlassian/parent-poms.git</connection>
        <developerConnection>scm:git:ssh://*****************/atlassian/parent-poms.git</developerConnection>
        <url>https://bitbucket.org/atlassian/parent-poms</url>
        <tag>HEAD</tag>
    </scm>

    <issueManagement>
        <system>JIRA</system>
        <url>http://jira.atlassian.com/browse/ABPOM</url>
    </issueManagement>
    <ciManagement>
        <system>Bamboo</system>
        <url>https://staging-bamboo.internal.atlassian.com/browse/OTHER-BASEPOM</url>
    </ciManagement>

    <distributionManagement>
        <repository>
            <id>atlassian-private</id>
            <name>Atlassian Private Repository</name>
            <url>https://maven.atlassian.com/private</url>
        </repository>
        <snapshotRepository>
            <id>atlassian-private-snapshot</id>
            <name>Atlassian Private Snapshot Repository</name>
            <url>https://maven.atlassian.com/private-snapshot</url>
        </snapshotRepository>
    </distributionManagement>

    <properties>
        <!-- Encoding settings -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <!-- maven-compiler-plugin config -->
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <clover.Version>3.1.5</clover.Version>
        <!-- maven-javadoc-plugin config -->
        <maven.javadoc.plugin.version>2.10.3</maven.javadoc.plugin.version>

        <!-- ban-milestones-and-release-candidates - may be overridden if absolutely necessary for a release -->
        <failOnMilestoneOrReleaseCandidateDeps>true</failOnMilestoneOrReleaseCandidateDeps>

        <!-- This is the version of checktyle that the checkstyle plugin uses, rather than the checkstyle plugin version -->
        <checkstyle.version>6.9</checkstyle.version>

        <!-- nexus-staging-maven-plugin config -->
        <nexus.staging.plugin.baseVersion>1.6.5</nexus.staging.plugin.baseVersion>
        <nexus.staging.plugin.fork>-atlassian-2</nexus.staging.plugin.fork>
        <nexus.staging.skip>false</nexus.staging.skip>
        <nexus.staging.serverUrl>https://maven.atlassian.com/staging/atlassian-central/</nexus.staging.serverUrl>
        <nexus.staging.serverId>atlassian-central</nexus.staging.serverId>
        <nexus.staging.profileId>atlassian-central-staging</nexus.staging.profileId>
        <nexus.staging.autoRelease>true</nexus.staging.autoRelease>
    </properties>

    <build>
        <extensions>
            <extension>
                <groupId>org.apache.maven.wagon</groupId>
                <artifactId>wagon-ssh-external</artifactId>
                <version>1.0</version>
            </extension>
        </extensions>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <inherited>false</inherited>
                <configuration>
                    <altDeploymentRepository>
                        atlassian-m2-snapshot-repository::default::https://maven.atlassian.com/central-snapshot
                    </altDeploymentRepository>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <dependencies>
                    <dependency>
                        <groupId>com.atlassian.maven.enforcer</groupId>
                        <artifactId>maven-enforcer-rules</artifactId>
                        <version>1.4.0</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>enforce-build-environment</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireJavaVersion>
                                    <version>1.8</version>
                                </requireJavaVersion>
                                <requireMavenVersion>
                                    <version>[3.2,)</version>
                                </requireMavenVersion>
                                <requirePluginVersions>
                                    <message>Best Practice is to always define plugin versions!</message>
                                </requirePluginVersions>
                            </rules>
                        </configuration>
                    </execution>

                    <!--
                      To turn off this enforcer rule:
                        specify
                            <failOnMilestoneOrReleaseCandidateDeps>false</failOnMilestoneOrReleaseCandidateDeps>
                        in your pom.xml
                        OR
                        use
                            -Darguments="-DfailOnMilestoneOrReleaseCandidateDeps=false"
                        on the command line.

                     In general, you don't want to turn off this rule, and remove your milestone and release candidate
                     dependencies!

                     If there are specific, known artifacts without any version other than RC or milestone, they may be
                     excluded in your project by including the following in pluginManagement:
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-enforcer-plugin</artifactId>
                            <executions>
                                <execution>
                                    <id>ban-milestones-and-release-candidates</id>
                                    <phase>validate</phase>
                                    <goals>
                                        <goal>enforce</goal>
                                    </goals>
                                    <configuration>
                                        <rules>
                                            <banVersionDeps>
                                                <excludes>
                                                    <exclude>com.atlassian.nobodycares:aboutme</exclude>
                                                    <exclude>com.google.octocatty:octoplusone</exclude>
                                                </excludes>
                                            </banVersionDeps>
                                        </rules>
                                    </configuration>
                                </execution>
                            </executions>
                        </plugin>
                    -->
                    <execution>
                        <id>ban-milestones-and-release-candidates</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <banVersionDeps implementation="com.atlassian.maven.enforcer.BanVersionDeps">
                                    <message>Milestone and Release Candidate dependencies are not allowed in releases.</message>

                                    <!-- ban dependencies with milestone or release candidate versions -->
                                    <bannedDependencyVersionRegexp>(?i)^.*-(rc|m)-?[0-9]+(-.+)?$</bannedDependencyVersionRegexp>

                                    <!-- let them off with a warning if they're building a milestone or snapshot -->
                                    <noFailReactorVersionRegexp>(?i)^.*-m-?[0-9]+(-.+)?$</noFailReactorVersionRegexp>
                                    <noFailSnapshots>true</noFailSnapshots>

                                    <!-- only ban direct dependencies -->
                                    <searchTransitive>false</searchTransitive>

                                    <!-- don't check test scoped dependencies -->
                                    <ignoreTest>true</ignoreTest>
                                </banVersionDeps>
                            </rules>
                            <fail>${failOnMilestoneOrReleaseCandidateDeps}</fail>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.6</version>
                <executions>
                    <execution>
                        <id>copy-license</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.outputDirectory}/META-INF</outputDirectory>
                            <overwrite>true</overwrite>
                            <resources>
                                <resource>
                                    <directory>${user.dir}</directory>
                                    <includes>
                                        <include>LICENSE.txt</include>
                                        <include>NOTICE.txt</include>
                                        <include>license.txt</include>
                                        <include>notice.txt</include>
                                    </includes>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.sonatype.plugins</groupId>
                <artifactId>nexus-staging-maven-plugin</artifactId>
                <extensions>true</extensions>
                <configuration>
                    <skipStaging>${nexus.staging.skip}</skipStaging>
                    <serverId>${nexus.staging.serverId}</serverId>
                    <nexusUrl>${nexus.staging.serverUrl}</nexusUrl>
                    <stagingProfileId>${nexus.staging.profileId}</stagingProfileId>
                    <autoReleaseAfterClose>${nexus.staging.autoRelease}</autoReleaseAfterClose>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <!-- begin Core plugins -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>2.4.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>2.5.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>2.12</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>2.5</version>
                    <configuration>
                        <encoding>UTF-8</encoding>
                        <nonFilteredFileExtensions>
                            <nonFilteredFileExtension>tif</nonFilteredFileExtension>
                            <nonFilteredFileExtension>tiff</nonFilteredFileExtension>
                            <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                            <nonFilteredFileExtension>swf</nonFilteredFileExtension>
                        </nonFilteredFileExtensions>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>3.4</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-verifier-plugin</artifactId>
                    <version>1.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>2.1.2</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${maven.javadoc.plugin.version}</version>
                    <configuration>
                        <!-- Disable missing @param, @return etc. errors in JDK8 Javadoc linting -->
                        <additionalparam>-Xdoclint:all -Xdoclint:-missing</additionalparam>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.9</version>
                    <configuration>
                        <excludes>
                            <exclude>**/*$*</exclude>
                        </excludes>
                    </configuration>
                </plugin>
                <!-- end Core plugins -->

                <!-- start packaging -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-ear-plugin</artifactId>
                    <version>2.3.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-ejb-plugin</artifactId>
                    <version>2.2.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>2.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-rar-plugin</artifactId>
                    <version>2.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>2.1.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>2.3</version>
                </plugin>
                <!-- end packaging -->

                <!-- Web application server helpers -->
                <plugin>
                    <groupId>org.codehaus.cargo</groupId>
                    <artifactId>cargo-maven2-plugin</artifactId>
                    <version>1.1.2</version>
                    <configuration>
                        <skip>${maven.test.skip}</skip>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.mortbay.jetty</groupId>
                    <artifactId>jetty-maven-plugin</artifactId>
                    <version>8.0.0.v20110901</version>
                </plugin>
                <!-- end Web application server helpers -->

                <!-- Start tools -->
                <plugin>
                    <groupId>com.atlassian.maven.plugins</groupId>
                    <artifactId>maven-upload-plugin</artifactId>
                    <version>1.1</version>
                </plugin>
                <plugin>
                    <groupId>com.atlassian.maven.plugins</groupId>
                    <artifactId>maven-atlassian-source-distribution-plugin</artifactId>
                    <version>1.6.8</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-ant-plugin</artifactId>
                    <version>2.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>1.6</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-archetype-plugin</artifactId>
                    <version>2.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>2.3</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>2.8</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>1.3.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>1.5</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-invoker-plugin</artifactId>
                    <version>1.5</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-patch-plugin</artifactId>
                    <version>1.1.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-plugin-plugin</artifactId>
                    <version>2.8</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>2.5</version>
                    <configuration>
                        <remoteTagging>true</remoteTagging>
                        <autoVersionSubmodules>true</autoVersionSubmodules>
                        <updateBranchVersions>true</updateBranchVersions>
                        <releaseProfiles>release</releaseProfiles>
                        <useReleaseProfile>true</useReleaseProfile>
                        <preparationGoals>clean install</preparationGoals>
                    </configuration>
                </plugin>
                <plugin>
                    <!-- Once the Atlassian fork is merged and released, remove the 'releaseParents' profile from central-pom - BUILDENG-8564 -->
                    <groupId>org.sonatype.plugins</groupId>
                    <artifactId>nexus-staging-maven-plugin</artifactId>
                    <version>${nexus.staging.plugin.baseVersion}${nexus.staging.plugin.fork}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.1</version>
                    <configuration>
                        <retryFailedDeploymentCount>3</retryFailedDeploymentCount>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-remote-resources-plugin</artifactId>
                    <version>1.5</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-repository-plugin</artifactId>
                    <version>2.3.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-scm-plugin</artifactId>
                    <version>1.5</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>2.4.0</version>
                </plugin>
                <plugin>
                    <groupId>com.atlassian.maven.plugins</groupId>
                    <artifactId>maven-atlassian-buildutils-plugin</artifactId>
                    <version>0.2</version>
                </plugin>
                <plugin>
                    <groupId>net.sf.alchim</groupId>
                    <artifactId>yuicompressor-maven-plugin</artifactId>
                    <version>0.7.1</version>
                </plugin>
                <!-- end tools -->

                <!-- start reporting -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jxr-plugin</artifactId>
                    <version>2.3</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-report-plugin</artifactId>
                    <version>2.9</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>findbugs-maven-plugin</artifactId>
                    <version>3.0.2</version>
                    <configuration>
                        <xmlOutput>true</xmlOutput>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>2.15</version>
                    <dependencies>
                        <dependency>
                            <groupId>com.puppycrawl.tools</groupId>
                            <artifactId>checkstyle</artifactId>
                            <version>${checkstyle.version}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-project-info-reports-plugin</artifactId>
                    <version>2.4</version>
                </plugin>
                <!--end reporting -->
                <!-- begin compliance -->
                <plugin>
                    <groupId>com.atlassian.maven.plugins</groupId>
                    <artifactId>license-maven-plugin</artifactId>
                    <version>0.27</version>
                </plugin>
                <!-- begin compliance -->
            </plugins>
        </pluginManagement>
    </build>

    <profiles>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-deploy-plugin</artifactId>
                        <inherited>false</inherited>
                        <configuration>
                            <altDeploymentRepository>
                                atlassian-m2-repository::default::https://maven.atlassian.com/central-staging
                            </altDeploymentRepository>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <inherited>false</inherited>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>checkstyle</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-remote-resources-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>process</goal>
                                </goals>
                                <configuration>
                                    <outputDirectory>${project.build.directory}/codestyle</outputDirectory>
                                    <resourceBundles>
                                        <resourceBundle>com.atlassian.codestyle:${atlassian.codestyle}:jar</resourceBundle>
                                    </resourceBundles>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-checkstyle-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>verify</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                                <configuration>
                                    <configLocation>${project.build.directory}/codestyle/checkstyle-rules.xml</configLocation>
                                    <propertyExpansion>configdir=${project.build.directory}/codestyle</propertyExpansion>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>2.4</version>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>issue-tracking</report>
                            <report>license</report>
                            <report>index</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>${maven.javadoc.plugin.version}</version>
            </plugin>
        </plugins>
    </reporting>
</project>
