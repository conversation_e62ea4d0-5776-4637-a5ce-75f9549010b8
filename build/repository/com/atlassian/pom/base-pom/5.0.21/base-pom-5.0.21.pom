<?xml version="1.0" ?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.atlassian.pom</groupId>
    <artifactId>base-pom</artifactId>
    <version>5.0.21</version>
    <packaging>pom</packaging>

    <name>Atlassian Base POM</name>
    <description>Base POM for Atlassian projects</description>
    <url>https://www.atlassian.com/</url>
    <licenses>
        <license>
            <name>Atlassian Customer Agreement</name>
            <url>https://www.atlassian.com/legal/customer-agreement</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
      <developer>
        <id>charlie</id>
        <name>Charlie</name>
        <email><EMAIL></email>
      </developer>
    </developers>
    <organization>
        <name>Atlassian</name>
        <url>https://www.atlassian.com/</url>
    </organization>

    <prerequisites>
        <maven>3.0.5</maven>
    </prerequisites>

    <scm>
        <connection>scm:git:ssh://*****************/atlassian/parent-poms.git</connection>
        <developerConnection>scm:git:ssh://*****************/atlassian/parent-poms.git</developerConnection>
        <url>https://bitbucket.org/atlassian/parent-poms</url>
        <tag>HEAD</tag>
    </scm>

    <issueManagement>
        <system>JIRA</system>
        <url>https://jira.atlassian.com/browse/ABPOM</url>
    </issueManagement>
    <ciManagement>
        <system>Bamboo</system>
        <url>https://staging-bamboo.internal.atlassian.com/browse/OTHER-BASEPOM</url>
    </ciManagement>
    <distributionManagement>
        <repository>
            <id>maven-atlassian-com</id>
            <name>Atlassian Private Repository</name>
            <url>https://packages.atlassian.com/maven/central</url>
        </repository>
        <snapshotRepository>
            <id>maven-atlassian-com</id>
            <name>Atlassian Private Snapshot Repository</name>
            <url>https://packages.atlassian.com/maven/central-snapshot</url>
        </snapshotRepository>
    </distributionManagement>

    <properties>
        <!-- Encoding settings -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <!-- maven-compiler-plugin config -->
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <clover.Version>3.1.5</clover.Version>
        <!-- maven-javadoc-plugin config -->
        <maven.javadoc.plugin.version>3.0.0-M1</maven.javadoc.plugin.version>
        <maven.jxr.plugin.version>2.5</maven.jxr.plugin.version>
        <maven.project-info-reports.plugin.version>2.9</maven.project-info-reports.plugin.version>

        <!-- ban-milestones-and-release-candidates - may be overridden if absolutely necessary for a release -->
        <failOnMilestoneOrReleaseCandidateDeps>true</failOnMilestoneOrReleaseCandidateDeps>
        <!-- Set this to false to check for banned dependencies while building SNAPSHOTs -->
        <banVersionDeps.noFailSnapshots>true</banVersionDeps.noFailSnapshots>

        <!-- maven-javadoc-plugin config: You may override this in your own pom.xml. For example, you can set
             <javadoc.additional.params>-Xdoclint:missing</javadoc.additional.params>
             to tune Javadoc linting for your project. By default, disable missing @param, @return etc.
             errors in JDK8 Javadoc linting -->
        <javadoc.additional.params>-Xdoclint:all -Xdoclint:-missing</javadoc.additional.params>

        <!-- This is the version of checktyle that the checkstyle plugin uses, rather than the checkstyle plugin version -->
        <checkstyle.version>6.9</checkstyle.version>

        <artifactory.staging.skip>false</artifactory.staging.skip>
        <artifactory.staging.skipPromotion>false</artifactory.staging.skipPromotion>
        <artifactory.staging.serverId>maven-atlassian-com</artifactory.staging.serverId>
        <artifactory.staging.repo>maven-staging-local</artifactory.staging.repo>
        <artifactory.target.repo>maven-central-local</artifactory.target.repo>

        <aether.checksums.forSignature>true</aether.checksums.forSignature>
    </properties>

    <build>
        <extensions>
            <extension>
                <groupId>org.apache.maven.wagon</groupId>
                <artifactId>wagon-ssh-external</artifactId>
                <version>1.0</version>
            </extension>
        </extensions>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <inherited>false</inherited>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <dependencies>
                    <dependency>
                        <groupId>com.atlassian.maven.enforcer</groupId>
                        <artifactId>maven-enforcer-rules</artifactId>
                        <version>1.4.0</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>enforce-build-environment</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireJavaVersion>
                                    <version>1.8</version>
                                </requireJavaVersion>
                                <requireMavenVersion>
                                    <version>[3.2.5,)</version>
                                </requireMavenVersion>
                                <requirePluginVersions>
                                    <message>Best Practice is to always define plugin versions!</message>
                                </requirePluginVersions>
                            </rules>
                        </configuration>
                    </execution>

                    <!--
                      To turn off this enforcer rule:
                        specify
                            <failOnMilestoneOrReleaseCandidateDeps>false</failOnMilestoneOrReleaseCandidateDeps>
                        in your pom.xml
                        OR
                        use
                            -Darguments="-DfailOnMilestoneOrReleaseCandidateDeps=false"
                        on the command line.

                     In general, you don't want to turn off this rule, and remove your milestone and release candidate
                     dependencies!

                     If there are specific, known artifacts without any version other than RC or milestone, they may be
                     excluded in your project by including the following in pluginManagement:
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-enforcer-plugin</artifactId>
                            <executions>
                                <execution>
                                    <id>ban-milestones-and-release-candidates</id>
                                    <phase>validate</phase>
                                    <goals>
                                        <goal>enforce</goal>
                                    </goals>
                                    <configuration>
                                        <rules>
                                            <banVersionDeps>
                                                <excludes>
                                                    <exclude>com.atlassian.nobodycares:aboutme</exclude>
                                                    <exclude>com.google.octocatty:octoplusone</exclude>
                                                </excludes>
                                            </banVersionDeps>
                                        </rules>
                                    </configuration>
                                </execution>
                            </executions>
                        </plugin>
                    -->
                    <execution>
                        <id>ban-milestones-and-release-candidates</id>
                        <!--
                        We do this in the compile phase since the enforce-banned-dependencies rule (that this rule
                        derives from) will look externally for dependencies that exist in the reactor up until the
                        compile phase.
                        -->
                        <phase>compile</phase>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <banVersionDeps implementation="com.atlassian.maven.enforcer.BanVersionDeps">
                                    <message>Milestone and Release Candidate dependencies are not allowed in releases.</message>

                                    <!-- ban dependencies with milestone or release candidate versions -->
                                    <bannedDependencyVersionRegexp>(?i)^.*-(rc|m)-?[0-9]+(-.+)?$</bannedDependencyVersionRegexp>

                                    <!-- let them off with a warning if they're building a milestone or snapshot -->
                                    <noFailReactorVersionRegexp>(?i)^.*-m-?[0-9]+(-.+)?$</noFailReactorVersionRegexp>
                                    <noFailSnapshots>${banVersionDeps.noFailSnapshots}</noFailSnapshots>

                                    <!-- only ban direct dependencies -->
                                    <searchTransitive>false</searchTransitive>

                                    <!-- don't check test scoped dependencies -->
                                    <ignoreTest>true</ignoreTest>
                                </banVersionDeps>
                            </rules>
                            <fail>${failOnMilestoneOrReleaseCandidateDeps}</fail>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <!-- version 3.0.2 has a performance regression BUILDENG-13628 -->
                <version>2.6</version>
                <executions>
                    <execution>
                        <id>copy-license</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.outputDirectory}/META-INF</outputDirectory>
                            <overwrite>true</overwrite>
                            <resources>
                                <resource>
                                    <directory>${user.dir}</directory>
                                    <includes>
                                        <include>LICENSE.txt</include>
                                        <include>NOTICE.txt</include>
                                        <include>license.txt</include>
                                        <include>notice.txt</include>
                                    </includes>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <!-- begin Core plugins -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>2.5.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <!-- https://github.com/spring-projects/spring-boot/issues/6254#issuecomment-229599865 -->
                    <version>2.18.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <!-- version 3.0.2 has a performance regression BUILDENG-13628 -->
                    <version>2.6</version>
                    <configuration>
                        <encoding>UTF-8</encoding>
                        <nonFilteredFileExtensions>
                            <nonFilteredFileExtension>tif</nonFilteredFileExtension>
                            <nonFilteredFileExtension>tiff</nonFilteredFileExtension>
                            <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                            <nonFilteredFileExtension>swf</nonFilteredFileExtension>
                        </nonFilteredFileExtensions>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>3.6</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-verifier-plugin</artifactId>
                    <version>1.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${maven.javadoc.plugin.version}</version>
                    <configuration>
                        <additionalparam>${javadoc.additional.params}</additionalparam>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.7.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.22.0</version>
                    <configuration>
                        <excludes>
                            <exclude>**/*$*</exclude>
                        </excludes>
                    </configuration>
                </plugin>
                <!-- end Core plugins -->

                <!-- start packaging -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-ear-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.0.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-rar-plugin</artifactId>
                    <version>2.4</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>3.2.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>3.1.1</version>
                </plugin>
                <!-- end packaging -->

                <!-- Web application server helpers -->
                <plugin>
                    <groupId>org.codehaus.cargo</groupId>
                    <artifactId>cargo-maven2-plugin</artifactId>
                    <version>1.6.3</version>
                    <configuration>
                        <skip>${maven.test.skip}</skip>
                    </configuration>
                </plugin>
                <!-- jetty-maven-plugin has moved to groupId org.eclipse.jetty
                     this entry should be deleted soon. Keep it just in case some project
                     are still using it
                -->
                <plugin>
                    <groupId>org.mortbay.jetty</groupId>
                    <artifactId>jetty-maven-plugin</artifactId>
                    <version>8.1.16.v20140903</version>
                </plugin>
                <plugin>
                    <groupId>org.eclipse.jetty</groupId>
                    <artifactId>jetty-maven-plugin</artifactId>
                    <version>9.4.6.v20170531</version>
                </plugin>
                <!-- end Web application server helpers -->

                <!-- Start tools -->
                <plugin>
                    <groupId>com.atlassian.maven.plugins</groupId>
                    <artifactId>maven-upload-plugin</artifactId>
                    <version>1.1</version>
                </plugin>
                <plugin>
                    <groupId>com.atlassian.maven.plugins</groupId>
                    <artifactId>maven-atlassian-source-distribution-plugin</artifactId>
                    <version>4.2.6</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-ant-plugin</artifactId>
                    <version>2.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>1.8</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-archetype-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.1.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.0.0-M1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>1.6</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-invoker-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-patch-plugin</artifactId>
                    <version>1.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-plugin-plugin</artifactId>
                    <version>3.5.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>2.5.3</version>
                    <configuration>
                        <remoteTagging>true</remoteTagging>
                        <autoVersionSubmodules>true</autoVersionSubmodules>
                        <updateBranchVersions>true</updateBranchVersions>
                        <releaseProfiles>release</releaseProfiles>
                        <useReleaseProfile>true</useReleaseProfile>
                        <preparationGoals>clean install</preparationGoals>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                    <configuration>
                        <retryFailedDeploymentCount>3</retryFailedDeploymentCount>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-remote-resources-plugin</artifactId>
                    <version>1.5</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-repository-plugin</artifactId>
                    <version>2.4</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-scm-plugin</artifactId>
                    <version>1.9.5</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>3.5.0</version>
                </plugin>
                <plugin>
                    <groupId>com.atlassian.maven.plugins</groupId>
                    <artifactId>maven-atlassian-buildutils-plugin</artifactId>
                    <version>0.2</version>
                </plugin>
                <plugin>
                    <groupId>net.alchim31.maven</groupId>
                    <artifactId>yuicompressor-maven-plugin</artifactId>
                    <version>1.5.1</version>
                </plugin>
                <!-- end tools -->

                <!-- start reporting -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jxr-plugin</artifactId>
                    <version>${maven.jxr.plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-report-plugin</artifactId>
                    <version>2.20.1</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>findbugs-maven-plugin</artifactId>
                    <version>3.0.2</version>
                    <configuration>
                        <xmlOutput>true</xmlOutput>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>2.17</version>
                    <dependencies>
                        <dependency>
                            <groupId>com.puppycrawl.tools</groupId>
                            <artifactId>checkstyle</artifactId>
                            <version>${checkstyle.version}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-project-info-reports-plugin</artifactId>
                    <version>${maven.project-info-reports.plugin.version}</version>
                </plugin>
                <!--end reporting -->
                <!-- begin compliance -->
                <plugin>
                    <groupId>com.atlassian.maven.plugins</groupId>
                    <artifactId>license-maven-plugin</artifactId>
                    <version>0.39</version>
                </plugin>
                <!-- begin compliance -->
                <plugin>
                        <groupId>com.atlassian.maven.plugins</groupId>
                        <artifactId>artifactory-staging-maven-plugin</artifactId>
                        <version>2.0.0</version>
                        <extensions>true</extensions>
                        <configuration>
                            <skip>${artifactory.staging.skip}</skip>
                            <skipPromotion>${artifactory.staging.skipPromotion}</skipPromotion>
                            <serverId>${artifactory.staging.serverId}</serverId>
                            <stagingRepository>${artifactory.staging.repo}</stagingRepository>
                            <targetRepository>${artifactory.target.repo}</targetRepository>
                            <artifactoryUrl>https://packages.atlassian.com</artifactoryUrl>
                        </configuration>
                        <executions>
                            <execution>
                                <id>staging</id>
                                <phase>validate</phase>
                                <goals>
                                    <goal>staging</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <profiles>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <inherited>false</inherited>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>checkstyle</id>
            <activation>
                <property>
                    <name>atlassian.codestyle</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-remote-resources-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>process</goal>
                                </goals>
                                <configuration>
                                    <outputDirectory>${project.build.directory}/codestyle</outputDirectory>
                                    <resourceBundles>
                                        <resourceBundle>com.atlassian.codestyle:${atlassian.codestyle}:jar</resourceBundle>
                                    </resourceBundles>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-checkstyle-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>verify</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                                <configuration>
                                    <configLocation>${project.build.directory}/codestyle/checkstyle-rules.xml</configLocation>
                                    <propertyExpansion>configdir=${project.build.directory}/codestyle</propertyExpansion>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>sox</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.6</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>${maven-project-info-reports-plugin}</version>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>issue-tracking</report>
                            <report>license</report>
                            <report>index</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>${maven.javadoc.plugin.version}</version>
            </plugin>
        </plugins>
    </reporting>
</project>
