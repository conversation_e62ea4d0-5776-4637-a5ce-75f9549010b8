<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.atlassian.pom</groupId>
        <artifactId>public-pom</artifactId>
        <version>5.0.21</version>
    </parent>

    <groupId>com.atlassian.httpclient</groupId>
    <artifactId>atlassian-httpclient-parent</artifactId>
    <version>2.0.0</version>
    <packaging>pom</packaging>

    <name>Atlassian HttpClientProject</name>
    <description>Project for the HttpClient</description>
    <inceptionYear>2012</inceptionYear>
    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>http://www.apache.org/licenses/</url>
        </license>
    </licenses>

    <modules>
        <module>httpclient-api</module>
        <module>httpclient-library</module>
        <module>httpclient-plugin</module>
    </modules>

    <scm>
        <connection>scm:git:*****************:atlassian/atlassian-httpclient.git</connection>
        <developerConnection>scm:git:*****************:atlassian/atlassian-httpclient.git</developerConnection>
        <url>https://bitbucket.org/atlassian/atlassian-httpclient</url>
        <tag>atlassian-httpclient-2.0.0</tag>
    </scm>
    <issueManagement>
        <system>Jira</system>
        <url>https://ecosystem.atlassian.net/browse/AHTTP</url>
    </issueManagement>
    <ciManagement>
        <system>Bamboo</system>
        <url>https://ecosystem-bamboo.internal.atlassian.com/browse/AHCP</url>
    </ciManagement>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <product>refapp</product>
        <product.version>5.0.0-m7</product.version>
        <product.data.version>${product.version}</product.data.version>

        <amps.version>8.0.0-m6</amps.version>
        <platform.version>5.0.0-m2</platform.version>

        <!-- Non-platform dependencies -->
        <analytics.version>2.12</analytics.version>
        <commons-codec.version>1.9</commons-codec.version>
        <httpasyncclient.version>4.1.4</httpasyncclient.version>
        <httpcore.version>4.4.10</httpcore.version>
        <httpclient.version>4.5.5</httpclient.version>
        <jetty.version>9.4.12.v20180830</jetty.version>
        <jackson2.version>2.9.2</jackson2.version>
        <log4j.version>1.2.17</log4j.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.atlassian.platform</groupId>
                <artifactId>platform</artifactId>
                <version>${platform.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.atlassian.platform</groupId>
                <artifactId>third-party</artifactId>
                <version>${platform.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.atlassian.httpclient</groupId>
                <artifactId>atlassian-httpclient-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.httpclient</groupId>
                <artifactId>atlassian-httpclient-library</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.atlassian.analytics</groupId>
                <artifactId>analytics-api</artifactId>
                <version>${analytics.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.junit</groupId>
                <artifactId>atlassian-junit</artifactId>
                <version>0.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.eclipse.jetty</groupId>
                        <artifactId>jetty-server</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.eclipse.jetty</groupId>
                        <artifactId>jetty-servlet</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.stefanbirkner</groupId>
                <artifactId>system-rules</artifactId>
                <version>1.5.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>log4j</groupId>
                <artifactId>log4j</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpasyncclient</artifactId>
                <version>${httpasyncclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpasyncclient-cache</artifactId>
                <version>${httpasyncclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient-cache</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>${httpcore.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore-nio</artifactId>
                <version>${httpcore.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>${httpclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-server</artifactId>
                <version>${jetty.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-servlet</artifactId>
                <version>${jetty.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.atlassian.maven.plugins</groupId>
                    <artifactId>amps-maven-plugin</artifactId>
                    <version>${amps.version}</version>
                    <extensions>true</extensions>
                </plugin>
                <plugin>
                    <groupId>com.atlassian.maven.plugins</groupId>
                    <artifactId>amps-dispatcher-maven-plugin</artifactId>
                    <version>${amps.version}</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <configuration>
                        <tagNameFormat>atlassian-httpclient-@{project.version}</tagNameFormat>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <configuration>
                        <doclint>none</doclint>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <configuration>
                    <rules>
                        <bannedDependencies>
                            <excludes>
                                <exclude>org.mockito:mockito-all</exclude>
                                <exclude>org.hamcrest:hamcrest-all</exclude>
                                <exclude>com.google.collections:google-collections</exclude>
                                <exclude>junit:junit-dep</exclude>
                                <exclude>javax.servlet:servlet-api</exclude>
                            </excludes>
                        </bannedDependencies>
                    </rules>
                    <fail>true</fail>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>jira</id>
            <properties>
                <product>jira</product>
                <product.version>8.0.0-QR-20181022070220</product.version>
            </properties>
        </profile>
        <profile>
            <id>confluence</id>
            <properties>
                <product>confluence</product>
                <product.version>6.4.3</product.version>
            </properties>
        </profile>
    </profiles>
</project>
