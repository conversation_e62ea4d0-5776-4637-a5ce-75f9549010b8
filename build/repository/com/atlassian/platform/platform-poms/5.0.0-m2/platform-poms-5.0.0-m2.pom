<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.atlassian.pom</groupId>
        <artifactId>public-pom</artifactId>
        <version>5.0.21</version>
    </parent>

    <groupId>com.atlassian.platform</groupId>
    <artifactId>platform-poms</artifactId>
    <version>5.0.0-m2</version>
    <packaging>pom</packaging>

    <name>Atlassian Platform POMs</name>
    <licenses>
        <license>
            <name>BSD License</name>
            <url>https://maven.atlassian.com/public/licenses/license.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <modules>
        <module>third-party</module>
        <module>platform</module>
    </modules>

    <scm>
        <connection>scm:git:*****************:atlassian/platform-poms.git</connection>
        <developerConnection>scm:git:*****************:atlassian/platform-poms.git</developerConnection>
        <url>https://bitbucket.org/atlassian/platform-poms</url>
      <tag>v5.0.0-m2</tag>
    </scm>

    <issueManagement>
        <system>Jira</system>
        <url>https://ecosystem.atlassian.net/browse/PLATFORM</url>
    </issueManagement>

    <ciManagement>
        <system>Bamboo</system>
        <url>https://ecosystem-bamboo.internal.atlassian.com/browse/JPP</url>
    </ciManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <configuration>
                        <autoVersionSubmodules>true</autoVersionSubmodules>
                        <tagNameFormat>v@{project.version}</tagNameFormat>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
