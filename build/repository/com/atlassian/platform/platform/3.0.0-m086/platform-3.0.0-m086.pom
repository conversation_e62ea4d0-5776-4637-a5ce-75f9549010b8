<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>platform-poms</artifactId>
        <groupId>com.atlassian.platform</groupId>
        <version>3.0.0-m086</version>
    </parent>

    <artifactId>platform</artifactId>
    <name>Atlassian Java Platform POM</name>
    <description>POM containing platform modules (libraries + plugins) for inclusion in the product</description>
    <packaging>pom</packaging>

    <properties>
        <activeobjects.version>0.30.0-m005</activeobjects.version>
        <annotations.version>0.16</annotations.version>
        <apl.version>5.0.0-m002</apl.version>
        <atr.version>2.0.0</atr.version>
        <cache.version>2.8.0</cache.version>
        <event.version>3.0.0</event.version>
        <failure-cache.version>0.15</failure-cache.version>
        <fugue.version>2.2.1</fugue.version>
        <healthcheck.version>3.0.1</healthcheck.version>
        <jquery.version>2.1.3</jquery.version>
        <landlord.version>2.0.1</landlord.version>
        <oauth.version>2.0.1</oauth.version>
        <plug.version>4.0.0</plug.version>
        <prettyurls.version>2.0.0</prettyurls.version>
        <rest.version>3.0.0</rest.version>
        <sal.version>3.0.0</sal.version>
        <strm.version>6.0.0</strm.version>
        <tenancy.version>2.0.0</tenancy.version>
        <trust.version>4.1.0</trust.version>
        <upm.version>2.19-rc6</upm.version>
        <util-concurrent.version>3.0.0</util-concurrent.version>
        <velocity.htmlsafe.version>1.4</velocity.htmlsafe.version>
        <webfragments.version>4.0.0</webfragments.version>
        <webhooks.version>3.0.0</webhooks.version>
        <webresource.version>3.3.1</webresource.version>
    </properties>

    <!--
        If you add any more dependencies here please use this script to order them
        cat pom.xml | sed -e 's/ xmlns.*=".*"//g' | xmlstarlet sel -t -m "//dependencies/dependency" -s A:T:- "./groupId/text()" -c "."
    -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.atlassian</groupId>
                <artifactId>atlassian-failure-cache-plugin</artifactId>
                <version>${failure-cache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.activeobjects</groupId>
                <artifactId>activeobjects-plugin</artifactId>
                <version>${activeobjects.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.activeobjects</groupId>
                <artifactId>activeobjects-spi</artifactId>
                <version>${activeobjects.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.activeobjects</groupId>
                <artifactId>activeobjects-test</artifactId>
                <version>${activeobjects.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.activeobjects</groupId>
                <artifactId>activeobjects-jira-spi</artifactId>
                <version>${activeobjects.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.activeobjects</groupId>
                <artifactId>activeobjects-confluence-spi</artifactId>
                <version>${activeobjects.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.activeobjects</groupId>
                <artifactId>activeobjects-bamboo-spi</artifactId>
                <version>${activeobjects.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.activeobjects</groupId>
                <artifactId>activeobjects-refapp-spi</artifactId>
                <version>${activeobjects.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.annotations</groupId>
                <artifactId>atlassian-annotations</artifactId>
                <version>${annotations.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.applinks</groupId>
                <artifactId>applinks-api</artifactId>
                <version>${apl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.applinks</groupId>
                <artifactId>applinks-host</artifactId>
                <version>${apl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.applinks</groupId>
                <artifactId>applinks-spi</artifactId>
                <version>${apl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.applinks</groupId>
                <artifactId>applinks-plugin</artifactId>
                <version>${apl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.applinks</groupId>
                <artifactId>applinks-basicauth-plugin</artifactId>
                <version>${apl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.applinks</groupId>
                <artifactId>applinks-cors-plugin</artifactId>
                <version>${apl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.applinks</groupId>
                <artifactId>applinks-oauth-plugin</artifactId>
                <version>${apl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.applinks</groupId>
                <artifactId>applinks-trustedapps-plugin</artifactId>
                <version>${apl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.applinks</groupId>
                <artifactId>applinks-pageobjects</artifactId>
                <version>${apl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.cache</groupId>
                <artifactId>atlassian-cache-api</artifactId>
                <version>${cache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.cache</groupId>
                <artifactId>atlassian-cache-memory</artifactId>
                <version>${cache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.event</groupId>
                <artifactId>atlassian-event</artifactId>
                <version>${event.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.fugue</groupId>
                <artifactId>fugue</artifactId>
                <version>${fugue.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.healthcheck</groupId>
                <artifactId>atlassian-healthcheck</artifactId>
                <version>${healthcheck.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-api</artifactId>
                <version>${oauth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-consumer-spi</artifactId>
                <version>${oauth.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.sourceforge.findbugs</groupId>
                        <artifactId>jsr305</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-service-provider-spi</artifactId>
                <version>${oauth.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.sourceforge.findbugs</groupId>
                        <artifactId>jsr305</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-consumer-sal-plugin</artifactId>
                <version>${oauth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-consumer-plugin</artifactId>
                <version>${oauth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-service-provider-plugin</artifactId>
                <version>${oauth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-service-provider-sal-plugin</artifactId>
                <version>${oauth.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.sourceforge.findbugs</groupId>
                        <artifactId>jsr305</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-admin-plugin</artifactId>
                <version>${oauth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-bridge</artifactId>
                <version>${oauth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-signature-generator-plugin</artifactId>
                <version>${oauth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>jquery</artifactId>
                <version>${jquery.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-api</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-core</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-osgi</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-osgi-bridge</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-servlet</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-webfragment</artifactId>
                <version>${webfragments.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-webresource</artifactId>
                <version>${webresource.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-webresource-plugin</artifactId>
                <version>${webresource.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-framework-bundles</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-main</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-spring</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-webresource-common</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-schema</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-landlord-plugin</artifactId>
                <version>${landlord.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-landlord-spi</artifactId>
                <version>${landlord.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins.rest</groupId>
                <artifactId>atlassian-rest-common</artifactId>
                <version>${rest.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins.rest</groupId>
                <artifactId>com.atlassian.jersey-library</artifactId>
                <type>pom</type>
                <version>${rest.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins.rest</groupId>
                <artifactId>atlassian-rest-module</artifactId>
                <version>${rest.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins.test</groupId>
                <artifactId>atlassian-plugins-test</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.prettyurls</groupId>
                <artifactId>atlassian-pretty-urls-plugin</artifactId>
                <version>${prettyurls.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.sal</groupId>
                <artifactId>sal-api</artifactId>
                <version>${sal.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.sal</groupId>
                <artifactId>sal-core</artifactId>
                <version>${sal.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.sal</groupId>
                <artifactId>sal-spi</artifactId>
                <version>${sal.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.sal</groupId>
                <artifactId>sal-spring</artifactId>
                <version>${sal.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.sal</groupId>
                <artifactId>sal-test-resources</artifactId>
                <version>${sal.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.sal</groupId>
                <artifactId>auiplugin-integration-sal</artifactId>
                <version>${sal.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.sal</groupId>
                <artifactId>sal-trust-api</artifactId>
                <version>${sal.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.sal</groupId>
                <artifactId>sal-trusted-apps-plugin-support</artifactId>
                <version>${sal.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.security.auth.trustedapps</groupId>
                <artifactId>atlassian-trusted-apps-core</artifactId>
                <version>${trust.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.security.auth.trustedapps</groupId>
                <artifactId>atlassian-trusted************************</artifactId>
                <version>${trust.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.streams</groupId>
                <artifactId>streams-api</artifactId>
                <version>${strm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.streams</groupId>
                <artifactId>streams-spi</artifactId>
                <version>${strm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.streams</groupId>
                <artifactId>streams-core-plugin</artifactId>
                <version>${strm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.streams</groupId>
                <artifactId>streams-inline-actions-plugin</artifactId>
                <version>${strm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.streams</groupId>
                <artifactId>streams-aggregator-plugin</artifactId>
                <version>${strm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.streams</groupId>
                <artifactId>streams-bamboo-plugin</artifactId>
                <version>${strm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.templaterenderer</groupId>
                <artifactId>atlassian-template-renderer-api</artifactId>
                <version>${atr.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.templaterenderer</groupId>
                <artifactId>atlassian-template-renderer-velocity16-plugin</artifactId>
                <version>${atr.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.tenancy</groupId>
                <artifactId>atlassian-tenancy-api</artifactId>
                <version>${tenancy.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.tenancy</groupId>
                <artifactId>atlassian-tenancy-compatibility-plugin</artifactId>
                <version>${tenancy.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.upm</groupId>
                <artifactId>atlassian-************************-plugin</artifactId>
                <version>${upm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.upm</groupId>
                <artifactId>spi</artifactId>
                <version>${upm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.util.concurrent</groupId>
                <artifactId>atlassian-util-concurrent</artifactId>
                <version>${util-concurrent.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.velocity.htmlsafe</groupId>
                <artifactId>velocity-htmlsafe</artifactId>
                <version>${velocity.htmlsafe.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.webhooks</groupId>
                <artifactId>atlassian-webhooks-plugin</artifactId>
                <version>${webhooks.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.atlassian.webhooks</groupId>
                <artifactId>atlassian-webhooks-provider-spi</artifactId>
                <version>${webhooks.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
