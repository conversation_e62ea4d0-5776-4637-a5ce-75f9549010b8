<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.atlassian.platform</groupId>
        <artifactId>platform-poms</artifactId>
        <version>5.0.0-m2</version>
    </parent>

    <artifactId>platform</artifactId>
    <packaging>pom</packaging>

    <name>Atlassian Java Platform POM</name>
    <description>POM containing platform modules (libraries + plugins) for inclusion in the product</description>

    <properties>
        <activeobjects.version>3.0.0-m02</activeobjects.version>
        <annotations.version>2.1.0</annotations.version>
        <apl.version>6.0.0-090a818f</apl.version>
        <atlassian.http.version>2.0.2</atlassian.http.version>
        <atlassian.httpclient.version>2.0.0-m1</atlassian.httpclient.version>
        <atr.version>4.0.0-71ca514</atr.version>
        <beehive.version>2.0.0-8ce05cb7</beehive.version>
        <cache.version>4.0.0-cdee60a</cache.version>
        <caesium.version>3.0.0-m1</caesium.version>
        <event.version>4.0.0-3937a39</event.version>
        <failure-cache.version>2.0.0-m1</failure-cache.version>
        <fugue.version>4.7.2</fugue.version>
        <healthcheck.version>6.0.0-fc24292</healthcheck.version>
        <jquery.version>*******</jquery.version>
        <landlord.version>3.0.0-b90d422</landlord.version>
        <marshalling-api.version>1.0.0</marshalling-api.version>
        <oauth.version>4.0.1-b45ab12e</oauth.version>
        <!-- when you change plugins version, make sure spring version in third-party is the same that plugins provides -->
        <plug.version>5.0.0-m1</plug.version>
        <plug.classifier>spring50x</plug.classifier>
        <prettyurls.version>3.0.0-a256463</prettyurls.version>
        <rest.version>6.0.0-985fbb15</rest.version>
        <sal.version>4.0.0-e2407f64</sal.version>
        <scheduler.version>3.0.0-6d46a01</scheduler.version>
        <soy.templates.version>5.0.0-m2</soy.templates.version>
        <spring.scanner.version>2.1.7</spring.scanner.version>
        <strm.version>8.0.0-m1</strm.version>
        <tenancy.version>3.0.0-5af03e1</tenancy.version>
        <trust.version>5.0.0-e059a5f3</trust.version>
        <util-concurrent.version>4.0.1</util-concurrent.version>
        <!-- When upgrading VCache, upgrade marshalling-api too -->
        <vcache.version>1.12.0</vcache.version>
        <velocity.htmlsafe.version>3.0.0-10821a07</velocity.htmlsafe.version>
        <webfragments.version>5.0.0-626d3f5</webfragments.version>
        <webhooks.version>6.0.0-m2</webhooks.version>
        <webresource.version>4.0.0-5988c2a</webresource.version>
    </properties>

    <!--
        If you add any more dependencies here please use this script to order them
        cat pom.xml | sed -e 's/ xmlns.*=".*"//g' | xmlstarlet sel -t -m "//dependencies/dependency" -s A:T:- "./groupId/text()" -c "."
    -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.atlassian</groupId>
                <artifactId>atlassian-failure-cache-plugin</artifactId>
                <version>${failure-cache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.activeobjects</groupId>
                <artifactId>activeobjects-plugin</artifactId>
                <version>${activeobjects.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.activeobjects</groupId>
                <artifactId>activeobjects-spi</artifactId>
                <version>${activeobjects.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.activeobjects</groupId>
                <artifactId>activeobjects-test</artifactId>
                <version>${activeobjects.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.activeobjects</groupId>
                <artifactId>activeobjects-jira-spi</artifactId>
                <version>${activeobjects.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.activeobjects</groupId>
                <artifactId>activeobjects-confluence-spi</artifactId>
                <version>${activeobjects.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.activeobjects</groupId>
                <artifactId>activeobjects-bamboo-spi</artifactId>
                <version>${activeobjects.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.activeobjects</groupId>
                <artifactId>activeobjects-refapp-spi</artifactId>
                <version>${activeobjects.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.annotations</groupId>
                <artifactId>atlassian-annotations</artifactId>
                <version>${annotations.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.applinks</groupId>
                <artifactId>applinks-api</artifactId>
                <version>${apl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.applinks</groupId>
                <artifactId>applinks-host</artifactId>
                <version>${apl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.applinks</groupId>
                <artifactId>applinks-spi</artifactId>
                <version>${apl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.applinks</groupId>
                <artifactId>applinks-plugin</artifactId>
                <version>${apl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.applinks</groupId>
                <artifactId>applinks-basicauth-plugin</artifactId>
                <version>${apl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.applinks</groupId>
                <artifactId>applinks-cors-plugin</artifactId>
                <version>${apl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.applinks</groupId>
                <artifactId>applinks-oauth-plugin</artifactId>
                <version>${apl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.applinks</groupId>
                <artifactId>applinks-trustedapps-plugin</artifactId>
                <version>${apl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.applinks</groupId>
                <artifactId>applinks-pageobjects</artifactId>
                <version>${apl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.beehive</groupId>
                <artifactId>beehive-api</artifactId>
                <version>${beehive.version}</version>
            </dependency><dependency>
                <groupId>com.atlassian.beehive</groupId>
                <artifactId>beehive-core</artifactId>
                <version>${beehive.version}</version>
            </dependency><dependency>
                <groupId>com.atlassian.beehive</groupId>
                <artifactId>beehive-core-tck</artifactId>
                <version>${beehive.version}</version>
            </dependency><dependency>
                <groupId>com.atlassian.beehive</groupId>
                <artifactId>beehive-db</artifactId>
                <version>${beehive.version}</version>
            </dependency><dependency>
                <groupId>com.atlassian.beehive</groupId>
                <artifactId>beehive-hazelcast</artifactId>
                <version>${beehive.version}</version>
            </dependency><dependency>
                <groupId>com.atlassian.beehive</groupId>
                <artifactId>beehive-single-node</artifactId>
                <version>${beehive.version}</version>
            </dependency><dependency>
                <groupId>com.atlassian.cache</groupId>
                <artifactId>atlassian-cache-api</artifactId>
                <version>${cache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.cache</groupId>
                <artifactId>atlassian-cache-memory</artifactId>
                <version>${cache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.event</groupId>
                <artifactId>atlassian-event</artifactId>
                <version>${event.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.healthcheck</groupId>
                <artifactId>atlassian-healthcheck</artifactId>
                <version>${healthcheck.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.healthcheck</groupId>
                <artifactId>atlassian-healthcheck-spi</artifactId>
                <version>${healthcheck.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.healthcheck</groupId>
                <artifactId>atlassian-healthcheck-in-product-test-support</artifactId>
                <version>${healthcheck.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.http</groupId>
                <artifactId>atlassian-http</artifactId>
                <version>${atlassian.http.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.httpclient</groupId>
                <artifactId>atlassian-httpclient-api</artifactId>
                <version>${atlassian.httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.httpclient</groupId>
                <artifactId>atlassian-httpclient-plugin</artifactId>
                <version>${atlassian.httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.marshalling</groupId>
                <artifactId>atlassian-marshalling-api</artifactId>
                <version>${marshalling-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-api</artifactId>
                <version>${oauth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-consumer-spi</artifactId>
                <version>${oauth.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.sourceforge.findbugs</groupId>
                        <artifactId>jsr305</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-service-provider-spi</artifactId>
                <version>${oauth.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.sourceforge.findbugs</groupId>
                        <artifactId>jsr305</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-consumer-sal-plugin</artifactId>
                <version>${oauth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-consumer-plugin</artifactId>
                <version>${oauth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-service-provider-plugin</artifactId>
                <version>${oauth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-service-provider-sal-plugin</artifactId>
                <version>${oauth.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.sourceforge.findbugs</groupId>
                        <artifactId>jsr305</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-admin-plugin</artifactId>
                <version>${oauth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-bridge</artifactId>
                <version>${oauth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.oauth</groupId>
                <artifactId>atlassian-oauth-signature-generator-plugin</artifactId>
                <version>${oauth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugin</groupId>
                <artifactId>atlassian-spring-scanner-annotation</artifactId>
                <version>${spring.scanner.version}</version>
            </dependency><dependency>
                <groupId>com.atlassian.plugin</groupId>
                <artifactId>atlassian-spring-scanner-runtime</artifactId>
                <version>${spring.scanner.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>jquery</artifactId>
                <version>${jquery.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-api</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-core</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-osgi</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-osgi-bridge</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-servlet</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-eventlistener</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-webfragment</artifactId>
                <version>${webfragments.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-webfragment-api</artifactId>
                <version>${webfragments.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-webresource</artifactId>
                <version>${webresource.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-webresource-plugin</artifactId>
                <version>${webresource.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-webresource-rest</artifactId>
                <version>${webresource.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-framework-bundles</artifactId>
                <version>${plug.version}</version>
                <type>zip</type>
                <classifier>${plug.classifier}</classifier>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-main</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-spring</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-webresource-common</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-plugins-schema</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-landlord-plugin</artifactId>
                <version>${landlord.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins</groupId>
                <artifactId>atlassian-landlord-spi</artifactId>
                <version>${landlord.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins.rest</groupId>
                <artifactId>atlassian-rest-common</artifactId>
                <version>${rest.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins.rest</groupId>
                <artifactId>com.atlassian.jersey-library</artifactId>
                <version>${rest.version}</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins.rest</groupId>
                <artifactId>atlassian-rest-module</artifactId>
                <version>${rest.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.plugins.test</groupId>
                <artifactId>atlassian-plugins-test</artifactId>
                <version>${plug.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.prettyurls</groupId>
                <artifactId>atlassian-pretty-urls-plugin</artifactId>
                <version>${prettyurls.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.sal</groupId>
                <artifactId>sal-api</artifactId>
                <version>${sal.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.sal</groupId>
                <artifactId>sal-core</artifactId>
                <version>${sal.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.sal</groupId>
                <artifactId>sal-spi</artifactId>
                <version>${sal.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.sal</groupId>
                <artifactId>sal-spring</artifactId>
                <version>${sal.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.sal</groupId>
                <artifactId>sal-test-resources</artifactId>
                <version>${sal.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.sal</groupId>
                <artifactId>auiplugin-integration-sal</artifactId>
                <version>${sal.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.sal</groupId>
                <artifactId>sal-trust-api</artifactId>
                <version>${sal.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.sal</groupId>
                <artifactId>sal-trusted-apps-plugin-support</artifactId>
                <version>${sal.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.scheduler</groupId>
                <artifactId>atlassian-scheduler-api</artifactId>
                <version>${scheduler.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.scheduler</groupId>
                <artifactId>atlassian-scheduler-core</artifactId>
                <version>${scheduler.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.scheduler</groupId>
                <artifactId>atlassian-scheduler-core-test</artifactId>
                <version>${scheduler.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.scheduler</groupId>
                <artifactId>atlassian-scheduler-quartz1</artifactId>
                <version>${scheduler.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.scheduler</groupId>
                <artifactId>atlassian-scheduler-quartz2</artifactId>
                <version>${scheduler.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.scheduler.caesium</groupId>
                <artifactId>atlassian-scheduler-caesium</artifactId>
                <version>${caesium.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.security.auth.trustedapps</groupId>
                <artifactId>atlassian-trusted-apps-core</artifactId>
                <version>${trust.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.security.auth.trustedapps</groupId>
                <artifactId>atlassian-trusted************************</artifactId>
                <version>${trust.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.soy</groupId>
                <artifactId>atlassian-soy-cli-support</artifactId>
                <version>${soy.templates.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.soy</groupId>
                <artifactId>atlassian-soy-core</artifactId>
                <version>${soy.templates.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.soy</groupId>
                <artifactId>atlassian-soy-spring-boot-support</artifactId>
                <version>${soy.templates.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.soy</groupId>
                <artifactId>atlassian-soy-spring-mvc-support</artifactId>
                <version>${soy.templates.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.soy</groupId>
                <artifactId>atlassian-soy-spring-support</artifactId>
                <version>${soy.templates.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.soy</groupId>
                <artifactId>soy-template-renderer-api</artifactId>
                <version>${soy.templates.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.soy</groupId>
                <artifactId>soy-template-renderer-plugin-api</artifactId>
                <version>${soy.templates.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.soy</groupId>
                <artifactId>soy-template-plugin</artifactId>
                <version>${soy.templates.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.streams</groupId>
                <artifactId>streams-api</artifactId>
                <version>${strm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.streams</groupId>
                <artifactId>streams-spi</artifactId>
                <version>${strm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.streams</groupId>
                <artifactId>streams-core-plugin</artifactId>
                <version>${strm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.streams</groupId>
                <artifactId>streams-inline-actions-plugin</artifactId>
                <version>${strm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.streams</groupId>
                <artifactId>streams-aggregator-plugin</artifactId>
                <version>${strm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.streams</groupId>
                <artifactId>streams-bamboo-plugin</artifactId>
                <version>${strm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.streams</groupId>
                <artifactId>streams-fisheye-plugin</artifactId>
                <version>${strm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.streams</groupId>
                <artifactId>streams-crucible-plugin</artifactId>
                <version>${strm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.templaterenderer</groupId>
                <artifactId>atlassian-template-renderer-api</artifactId>
                <version>${atr.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.templaterenderer</groupId>
                <artifactId>atlassian-template-renderer-velocity16-plugin</artifactId>
                <version>${atr.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.tenancy</groupId>
                <artifactId>atlassian-tenancy-api</artifactId>
                <version>${tenancy.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.tenancy</groupId>
                <artifactId>atlassian-tenancy-compatibility-plugin</artifactId>
                <version>${tenancy.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.vcache</groupId>
                <artifactId>atlassian-vcache-api</artifactId>
                <version>${vcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.vcache</groupId>
                <artifactId>atlassian-vcache-internal-api</artifactId>
                <version>${vcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.vcache</groupId>
                <artifactId>atlassian-vcache-internal-core</artifactId>
                <version>${vcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.vcache</groupId>
                <artifactId>atlassian-vcache-internal-guava</artifactId>
                <version>${vcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.vcache</groupId>
                <artifactId>atlassian-vcache-internal-legacy</artifactId>
                <version>${vcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.vcache</groupId>
                <artifactId>atlassian-vcache-internal-memcached</artifactId>
                <version>${vcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.vcache</groupId>
                <artifactId>atlassian-vcache-internal-redis</artifactId>
                <version>${vcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.vcache</groupId>
                <artifactId>atlassian-vcache-internal-harness</artifactId>
                <version>${vcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.vcache</groupId>
                <artifactId>atlassian-vcache-internal-test</artifactId>
                <version>${vcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.vcache</groupId>
                <artifactId>atlassian-vcache-internal-test-utils</artifactId>
                <version>${vcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.velocity.htmlsafe</groupId>
                <artifactId>velocity-htmlsafe</artifactId>
                <version>${velocity.htmlsafe.version}</version>
            </dependency>
            <dependency>
                <groupId>com.atlassian.webhooks</groupId>
                <artifactId>atlassian-webhooks-plugin</artifactId>
                <version>${webhooks.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.atlassian.webhooks</groupId>
                <artifactId>atlassian-webhooks-spi</artifactId>
                <version>${webhooks.version}</version>
            </dependency>
            <dependency>
                <groupId>io.atlassian.fugue</groupId>
                <artifactId>fugue</artifactId>
                <version>${fugue.version}</version>
            </dependency>
            <dependency>
                <groupId>io.atlassian.util.concurrent</groupId>
                <artifactId>atlassian-util-concurrent</artifactId>
                <version>${util-concurrent.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
