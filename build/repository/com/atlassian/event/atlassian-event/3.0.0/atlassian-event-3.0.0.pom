<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.atlassian.pom</groupId>
        <artifactId>public-pom</artifactId>
        <version>3.0.91</version>
    </parent>

    <groupId>com.atlassian.event</groupId>
    <artifactId>atlassian-event</artifactId>
    <version>3.0.0</version>

    <name>Atlassian Event</name>
    <description>Atlassian eventing system for use with Spring projects</description>
    <url>https://docs.atlassian.com/${project.artifactId}/${project.version}</url>
    <inceptionYear>2006</inceptionYear>

    <issueManagement>
        <system>JIRA</system>
        <url>https://ecosystem.atlassian.net/browse/EVENT</url>
    </issueManagement>

    <ciManagement>
        <system>Bamboo</system>
        <url>https://ecosystem-bamboo.internal.atlassian.com/browse/EVENT</url>
    </ciManagement>

    <scm>
        <connection>scm:git:*****************:atlassian/atlassian-event.git</connection>
        <developerConnection>scm:git:*****************:atlassian/atlassian-event.git</developerConnection>
        <url>https://bitbucket.org/atlassian/atlassian-event</url>
      <tag>atlassian-event-3.0.0</tag>
  </scm>

    <licenses>
        <license>
            <name>BSD License</name>
            <url>http://opensource.org/licenses/BSD-3-Clause</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.atlassian.platform</groupId>
                <artifactId>platform</artifactId>
                <version>${platform.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.atlassian.platform</groupId>
                <artifactId>third-party</artifactId>
                <version>${platform.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons.lang.version}</version>
            </dependency>

            <!-- to define the event module -->
            <dependency>
                <groupId>com.atlassian.inject</groupId>
                <artifactId>atlassian-inject</artifactId>
                <version>${inject.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.atlassian.plugins</groupId>
            <artifactId>atlassian-plugins-api</artifactId>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.atlassian.event</groupId>
                    <artifactId>atlassian-event</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.atlassian.plugins</groupId>
            <artifactId>atlassian-plugins-osgi</artifactId>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.atlassian.event</groupId>
                    <artifactId>atlassian-event</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.atlassian.util.concurrent</groupId>
            <artifactId>atlassian-util-concurrent</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- spring integration -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <optional>true</optional>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- to replace commons-logging coming from spring -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>

        <!-- to define the event module -->
        <dependency>
            <groupId>com.atlassian.inject</groupId>
            <artifactId>atlassian-inject</artifactId>
            <optional>true</optional>
            <exclusions>
                <exclusion>
                    <groupId>com.google.collections</groupId>
                    <artifactId>google-collections</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- test dependencies -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-library</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- this for the tests to run with an implementation of slf4j-api, this will > /dev/null all logs -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-nop</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <properties>
        <failOnMilestoneOrReleaseCandidateDeps>false</failOnMilestoneOrReleaseCandidateDeps>
        <maven.compiler.source>7</maven.compiler.source>
        <maven.compiler.target>7</maven.compiler.target>
        <commons.lang.version>2.4</commons.lang.version>
        <inject.version>1.0.0</inject.version>
        <platform.version>3.0.0-m073</platform.version>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>2.12</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>integration-test</goal>
                            <goal>verify</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <site>
            <id>atlassian-documentation</id>
            <url>scpexe://docs-app.internal.atlassian.com/var/www/domains/docs.atlassian.com/${project.artifactId}/${project.version}</url>
        </site>
    </distributionManagement>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.9</version>
            </plugin>
        </plugins>
    </reporting>

</project>
