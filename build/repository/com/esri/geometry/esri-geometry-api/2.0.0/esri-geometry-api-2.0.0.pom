<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.esri.geometry</groupId>
	<artifactId>esri-geometry-api</artifactId>
	<version>2.0.0</version>
	<packaging>jar</packaging>

	<name>Esri Geometry API for Java</name>
	<description>The Esri Geometry API for Java enables developers to write custom applications for analysis of spatial data.</description>

	<url>https://github.com/Esri/geometry-api-java</url>

	<licenses>
		<license>
				<name>The Apache Software License, Version 2.0</name>
				<url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
				<distribution>repo</distribution>
		</license>
	</licenses>

	<developers>
		<developer>
			<id>sto<PERSON>tov</id>
			<name><PERSON></name>
			<organization>Esri</organization>
			<organizationUrl>http://www.esri.com</organizationUrl>
			<roles>
				<role>developer</role>
			</roles>
		</developer>
		<developer>
			<id>abalog</id>
			<name>Aaron Balog</name>
			<organization>Esri</organization>
			<organizationUrl>http://www.esri.com</organizationUrl>
			<roles>
				<role>developer</role>
			</roles>
		</developer>
	</developers>

	<scm>
		<connection>scm:git:**************:Esri/geometry-api-java.git</connection>
		<developerConnection>scm:git:**************:Esri/geometry-api-java.git</developerConnection>
		<url>**************:Esri/geometry-api-java.git</url>
	</scm>

	<profiles>
		<profile>
			<id>release-sign-artifacts</id>
      <activation>
        <property>
          <name>performRelease</name>
          <value>true</value>
        </property>
      </activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-gpg-plugin</artifactId>
						<executions>
							<execution>
								<id>sign-artifacts</id>
								<phase>verify</phase>
								<goals>
									<goal>sign</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>java8-disable-doclint</id>
			<activation>
				<jdk>[1.8,)</jdk>
			</activation>
			<properties>
				<javadoc.doclint.param>-Xdoclint:none</javadoc.doclint.param>
			</properties>
		</profile>
	</profiles>

	<distributionManagement>
		<snapshotRepository>
			<id>ossrh</id>
			<url>https://oss.sonatype.org/content/repositories/snapshots</url>
		</snapshotRepository>
	</distributionManagement>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

		<java.source.version>1.6</java.source.version>
		<java.target.version>1.6</java.target.version>

		<!-- dependency versions -->
		<jackson.version>2.6.5</jackson.version>
		<junit.version>4.12</junit.version>

		<!-- plugin versions -->
		<compiler.plugin.version>2.3.1</compiler.plugin.version>
		<source.plugin.version>2.2.1</source.plugin.version>
		<javadoc.plugin.version>2.9</javadoc.plugin.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>${jackson.version}</version>
			<optional>false</optional>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>${junit.version}</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<directory>${project.basedir}/target</directory>
		<outputDirectory>${project.build.directory}/classes</outputDirectory>
		<finalName>${project.artifactId}-${project.version}</finalName>
		<testOutputDirectory>${project.build.directory}/test-classes</testOutputDirectory>
		<sourceDirectory>${project.basedir}/src/main/java</sourceDirectory>
		<scriptSourceDirectory>src/main/scripts</scriptSourceDirectory>
		<testSourceDirectory>${project.basedir}/src/test/java</testSourceDirectory>
		<resources>
			<resource>
				<directory>${project.basedir}/src/main/resources</directory>
			</resource>
		</resources>
		<testResources>
			<testResource>
				<directory>${project.basedir}/src/test/resources</directory>
			</testResource>
		</testResources>
		<plugins>
			<plugin>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>${compiler.plugin.version}</version>
					<configuration>
						<source>${java.source.version}</source>
						<target>${java.target.version}</target>
					</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>${source.plugin.version}</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>${javadoc.plugin.version}</version>
				<configuration>
					<link>http://help.arcgis.com/EN/sdk/10.0/Java_AO_ADF/api/arcobjects</link>
					<link>http://docs.oracle.com/javase/6/docs/api/</link>
				</configuration>
				<executions>
					<execution>
						<id>attach-javadocs</id>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<additionalparam>${javadoc.doclint.param}</additionalparam>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.sonatype.plugins</groupId>
				<artifactId>nexus-staging-maven-plugin</artifactId>
				<version>1.6.2</version>
				<extensions>true</extensions>
				<configuration>
					<serverId>ossrh</serverId>
					<nexusUrl>https://oss.sonatype.org/</nexusUrl>
					<autoReleaseAfterClose>true</autoReleaseAfterClose>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
