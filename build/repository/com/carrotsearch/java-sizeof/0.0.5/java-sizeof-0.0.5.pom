<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>7</version>
  </parent>

  <!-- Project info. -->
  <groupId>com.carrotsearch</groupId>
  <artifactId>java-sizeof</artifactId>
  <version>0.0.5</version>
  <packaging>jar</packaging>

  <name>java-sizeof</name>
  <description>Java Objects size estimation.</description>

  <!-- Extended project info. -->
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <organization>
    <name>Carrot Search s.c.</name>
    <url>http://www.carrotsearch.com</url>
  </organization>

  <!-- Global properties. -->
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>1.6</maven.compiler.source>
    <maven.compiler.target>1.6</maven.compiler.target>
  </properties>

  <!-- Dependencies. -->
  <dependencies>
    <dependency>
    	<groupId>junit</groupId>
    	<artifactId>junit</artifactId>
    	<version>4.10</version>
    	<type>jar</type>
    	<scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.carrotsearch</groupId>
      <artifactId>junit-benchmarks</artifactId>
      <version>0.3.0</version>
      <scope>test</scope>
    </dependency>
  </dependencies>


  <!-- Build tuning. -->
  <build>
    <defaultGoal>install</defaultGoal>
  </build>
</project>
