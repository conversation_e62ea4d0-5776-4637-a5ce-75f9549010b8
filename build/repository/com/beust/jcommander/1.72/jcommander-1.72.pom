<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.beust</groupId>
  <artifactId>jcommander</artifactId>
  <version>1.72</version>
  <name>jcommander</name>
  <description>Command line parsing</description>
  <url>http://jcommander.org</url>
  <licenses>
    <license>
      <name>Apache 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <name><PERSON><PERSON></name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>https://github.com/cbeust/jcommander.git</connection>
    <developerConnection>**************:cbeust/jcommander.git</developerConnection>
    <url>http://github.com/cbeust/jcommander</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>org.testng</groupId>
      <artifactId>testng</artifactId>
      <version>6.10</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
