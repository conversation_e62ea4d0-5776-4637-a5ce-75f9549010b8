<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fasterxml</groupId>
        <artifactId>oss-parent</artifactId>
        <version>24</version>
    </parent>
    <artifactId>classmate</artifactId>
    <name>ClassMate</name>
    <version>1.3.4</version>
    <packaging>bundle</packaging>
    <description>Library for introspecting types with full generic information
        including resolving of field and method types.
    </description>
    <url>http://github.com/FasterXML/java-classmate</url>
    <scm>
        <connection>scm:git:**************:FasterXML/java-classmate.git</connection>
        <developerConnection>scm:git:**************:FasterXML/java-classmate.git</developerConnection>
        <url>http://github.com/FasterXML/java-classmate</url>
      <tag>classmate-1.3.4</tag>
  </scm>
    <developers>
        <developer>
            <id>tatu</id>
            <name>Tatu Saloranta</name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>blangel</id>
            <name>Brian Langel</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <prerequisites>
        <maven>2.2.1</maven>
    </prerequisites>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <version.jdk>1.6</version.jdk>

	<osgi.export>com.fasterxml.classmate;version=${project.version},
com.fasterxml.classmate.*;version=${project.version}
</osgi.export>
	<osgi.private>com.fasterxml.classmate.util.*</osgi.private>

    </properties>

    <!-- Licensing -->
    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <organization>
        <name>fasterxml.com</name>
        <url>http://fasterxml.com</url>
    </organization>

    <dependencies>
        <!-- and for testing, JUnit is needed -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- As per [#38] add `Automatic-Module-Name` -->
            <plugin>
              <groupId>org.apache.felix</groupId>
              <artifactId>maven-bundle-plugin</artifactId>
              <configuration>
		<instructions combine.children="merge">
		  <Automatic-Module-Name>com.fasterxml.classmate</Automatic-Module-Name>
		</instructions>
              </configuration>
            </plugin>

            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.2</version>
                <configuration>
                    <source>${version.jdk}</source>
                    <target>${version.jdk}</target>
                </configuration>
            </plugin>
            <plugin><!-- plug-in to attach source bundle in repo -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.1.2</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                 <artifactId>maven-javadoc-plugin</artifactId>
                 <version>${version.plugin.javadoc}</version>
                 <configuration>
                    <source>${version.jdk}</source>
                    <target>${version.jdk}</target>
                    <encoding>UTF-8</encoding>
                    <maxmemory>512m</maxmemory>
                    <links>
	 	      <link>http://docs.oracle.com/javase/8/docs/api/</link>
                    </links>
                 </configuration>
                 <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                 </executions>
            </plugin>

        </plugins>
    </build>
    <profiles>
        <profile>
            <id>release-sign-artifacts</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.1</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
    <!-- NOTE: repositories from parent POM -->
</project>
