<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion> 
  <parent>
    <groupId>com.fasterxml</groupId>
    <artifactId>oss-parent</artifactId>
    <version>11</version>
  </parent>

  <groupId>com.fasterxml.jackson.core</groupId>
  <artifactId>jackson-annotations</artifactId>
  <name>Jackson-annotations</name>
  <version>2.3.1</version>
  <packaging>bundle</packaging>
  <description>Core annotations used for value types, used by Jackson data binding package.
  </description>

  <url>http://wiki.fasterxml.com/JacksonHome</url>
  <scm>
    <connection>scm:git:**************:FasterXML/jackson-annotations.git</connection>
    <developerConnection>scm:git:**************:FasterXML/jackson-annotations.git</developerConnection>
    <url>http://github.com/FasterXML/jackson-annotations</url>
    <tag>jackson-annotations-2.3.1</tag>
  </scm>

  <properties>
    <!--
     | Configuration properties for the OSGi maven-bundle-plugin
    -->
    <osgi.export>com.fasterxml.jackson.annotation.*;version=${project.version}</osgi.export>
  </properties>

  <build>
    <plugins>
      <!-- not much to debug, but let's add all the info -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <optimize>true</optimize>
          <debug>true</debug>
          <debuglevel>lines,source,vars</debuglevel>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
