<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion> 
  <parent>
    <groupId>com.fasterxml.jackson</groupId>
    <artifactId>jackson-base</artifactId>
    <version>2.10.5</version>
  </parent>
  <groupId>com.fasterxml.jackson.jaxrs</groupId>
  <artifactId>jackson-jaxrs-providers</artifactId>
  <name>Jackson JAX-RS</name>
  <version>2.10.5</version>
  <packaging>pom</packaging>
  <description>Parent for Jackson JAX-RS providers
  </description>

  <modules>
    <module>base</module>
    <module>datatypes</module>
    <module>cbor</module>
    <module>json</module>
    <module>smile</module>
    <module>xml</module>
    <module>yaml</module>
  </modules>
  <url>http://github.com/FasterXML/jackson-jaxrs-providers</url>    
  <scm>
    <connection>scm:git:**************:FasterXML/jackson-jaxrs-providers.git</connection>
    <developerConnection>scm:git:**************:FasterXML/jackson-jaxrs-providers.git</developerConnection>
    <url>http://github.com/FasterXML/jackson-jaxrs-providers</url>    
    <tag>jackson-jaxrs-providers-2.10.5</tag>
  </scm>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding> 

    <!--  Need Jersey+Jetty for testing; deps from DropWizard 1.2.9 -->
    <version.jersey>2.25.1</version.jersey>
    <!-- as per CVE-2019-10247 -->
    <version.jetty>9.4.17.v20190418</version.jetty>

    <!-- Needed to enable jax-rs 2.0 usage under OSGi -->
    <javax.ws.rs.version>[2.0,2.2)</javax.ws.rs.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>jakarta.xml.bind</groupId>
        <artifactId>jakarta.xml.bind-api</artifactId>
        <version>2.3.2</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>

    <!-- and we need JAX-RS annotations for testing as well; but usually provided
      by container (and app should definitely have direct dep too, when using annotations)
       -->
    <dependency>
      <groupId>javax.ws.rs</groupId>
      <!-- 05-Apr-2014, tatu: JAX-RS 2.x has different artifact-id, "javax.ws.rs-api"
        -->
      <artifactId>javax.ws.rs-api</artifactId>
      <version>2.1.1</version>
      <scope>provided</scope>
    </dependency>

    <!-- tests require JAX-RS impl; otherwise components fail to load
        (some oddity with API classes; should NOT be needed...)
      -->
    <dependency> <!-- all components use junit for testing -->
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-server</artifactId>
      <version>${version.jetty}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-servlet</artifactId>
      <version>${version.jetty}</version>
      <scope>test</scope>
    </dependency>
        
    <dependency>
      <groupId>org.glassfish.jersey.core</groupId>
      <artifactId>jersey-server</artifactId>
      <version>${version.jersey}</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.glassfish.jersey.containers</groupId>
      <artifactId>jersey-container-servlet</artifactId>
      <version>${version.jersey}</version>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <!-- Alas, need to include snapshot reference since otherwise can not find
       snapshot of parent... -->
  <repositories>
    <repository>
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
      <releases><enabled>false</enabled></releases>
      <snapshots><enabled>true</enabled></snapshots>
    </repository>
  </repositories>  

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>3.2.0</version>
          <configuration>
            <instructions>
              <_nouses>false</_nouses>
            </instructions>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>
</project>
