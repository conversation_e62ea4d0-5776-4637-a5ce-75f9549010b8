<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion> 

  <parent>
    <groupId>com.fasterxml.jackson.jaxrs</groupId>
    <artifactId>jackson-jaxrs-providers</artifactId>
    <version>2.3.1</version>
  </parent>
  <artifactId>jackson-jaxrs-json-provider</artifactId>
  <name>Jackson-JAXRS-JSON</name>
  <packaging>bundle</packaging>
  <description>Functionality to handle JSON input/output for JAX-RS implementations (like Jersey and RESTeasy) using standard Jackson data binding.
  </description>

  <properties>
    <!-- Generate PackageVersion.java into this directory. -->
    <packageVersion.dir>com/fasterxml/jackson/jaxrs/json</packageVersion.dir>
    <packageVersion.package>${project.groupId}.json</packageVersion.package>
    <osgi.export>${project.groupId}.json.*;version=${project.version}</osgi.export>
    <!-- NOTE: JAXB annotations module is optional dependency, need to try to mark
         as such here.
      -->
    <osgi.import>javax.ws.rs;version="${javax.ws.rs.version}"
,javax.ws.rs.core;version="${javax.ws.rs.version}"
,javax.ws.rs.ext;version="${javax.ws.rs.version}"
,com.fasterxml.jackson.annotation
,com.fasterxml.jackson.core
,com.fasterxml.jackson.core.type
,com.fasterxml.jackson.core.util
,com.fasterxml.jackson.databind
,com.fasterxml.jackson.databind.introspect
,com.fasterxml.jackson.databind.type
,com.fasterxml.jackson.databind.util
,com.fasterxml.jackson.jaxrs.base
,com.fasterxml.jackson.jaxrs.cfg
,com.fasterxml.jackson.module.jaxb;resolution:=optional
</osgi.import>
  </properties>

  <dependencies>
    <!-- builds on shared base JAX-RS handling code... -->
    <dependency>
      <artifactId>jackson-jaxrs-base</artifactId>
      <groupId>${project.groupId}</groupId>
      <version>${project.version}</version>
    </dependency>
    <!-- Extends Jackson core, mapper, and also (sort of optionally) on JAXB annotation handler -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>${version.jackson.core}</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>${version.jackson.core}</version>
    </dependency>
    <!-- also need JAXB annotation support -->
    <dependency> 
      <groupId>com.fasterxml.jackson.module</groupId>
      <artifactId>jackson-module-jaxb-annotations</artifactId>
      <version>${version.jackson.jaxb}</version>
    </dependency>

    <!-- test deps should come from parent, including jersey -->
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <!-- Inherited from oss-base. Generate PackageVersion.java.-->
	<groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
	<executions>
          <execution>
            <id>process-packageVersion</id>
            <phase>process-sources</phase>
          </execution>
	</executions>
      </plugin>
    </plugins>
  </build>
</project>
