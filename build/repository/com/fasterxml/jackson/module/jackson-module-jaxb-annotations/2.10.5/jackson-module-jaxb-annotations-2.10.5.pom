<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion> 
  <parent>
    <groupId>com.fasterxml.jackson.module</groupId>
    <artifactId>jackson-modules-base</artifactId>
    <version>2.10.5</version>
  </parent>
  <artifactId>jackson-module-jaxb-annotations</artifactId>
  <name>Jackson module: JAXB Annotations</name>
  <packaging>bundle</packaging>

  <description>Support for using JAXB annotations as an alternative to "native" Jackson annotations, for configuring
data-binding.
  </description>
  <url>https://github.com/FasterXML/jackson-modules-base</url>

  <properties>
   <!-- Generate PackageVersion.java into this directory. -->
    <packageVersion.dir>com/fasterxml/jackson/module/jaxb</packageVersion.dir>
    <packageVersion.package>${project.groupId}.jaxb</packageVersion.package>
    <!-- 22-Mar-2019, tatu: [modules-base#80]: Presence of JAF on the bundle classpath is only necessary
           when ser/deser'ing javax.a.DataHandlers
      -->
    <osgi.import>javax.activation;resolution:=optional,*</osgi.import>

    <version.jaxb.impl>2.3.2</version.jaxb.impl>
  </properties>
  <dependencies>
    <!-- Extends Jackson core and mapper; minor dep on annotations too (JsonInclude) -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>

    <!--  and actual JAXB annotations, types -->
    <dependency>
      <groupId>jakarta.xml.bind</groupId>
      <artifactId>jakarta.xml.bind-api</artifactId>
      <version>${version.jaxb.impl}</version>
    </dependency>

    <!-- 14-Mar-2019, tatu: Looks like this is needed for JDK11 and later
      -->
    <!-- 05-May-2019, tatu: Apparently these ain't it...
    <dependency>
      <groupId>javax.activation</groupId>
      <artifactId>javax.activation-api</artifactId>
      <version>1.2.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
          <groupId>com.jwebmp.thirdparty</groupId>
          <artifactId>javax.activation</artifactId>
          <version>0.67.0.5</version>
          <scope>provided</scope>
    </dependency>
-->
    <dependency>
      <groupId>jakarta.activation</groupId>
      <artifactId>jakarta.activation-api</artifactId>
      <version>1.2.1</version>
    </dependency>

    <!-- may also need JAXB impl for tests -->
    <dependency>
      <groupId>org.glassfish.jaxb</groupId>
      <artifactId>jaxb-runtime</artifactId>
      <version>${version.jaxb.impl}</version>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
      </plugin>
      <!--  14-Mar-2019, tatu: Add rudimentary JDK9+ module info. To build with JDK 8
             will have to use `moduleInfoFile` as anything else requires JDK 9+
        -->
      <plugin>
        <groupId>org.moditect</groupId>
        <artifactId>moditect-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>
