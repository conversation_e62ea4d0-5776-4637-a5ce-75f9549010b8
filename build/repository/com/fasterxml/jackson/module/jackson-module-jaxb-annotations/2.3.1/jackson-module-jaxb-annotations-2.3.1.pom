<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion> 

  <parent>
    <groupId>com.fasterxml</groupId>
    <artifactId>oss-parent</artifactId>
    <version>12</version>
  </parent>

  <groupId>com.fasterxml.jackson.module</groupId>
  <artifactId>jackson-module-jaxb-annotations</artifactId>
  <version>2.3.1</version>
  <name>Jackson-module-JAXB-annotations</name>
  <packaging>bundle</packaging>
  <description>Support for using JAXB annotations as an alternative to "native" Jackson annotations, for configuring data binding.
  </description>
  <url>http://wiki.fasterxml.com/JacksonJAXBAnnotations</url>

  <scm>
    <connection>scm:git:**************:FasterXML/jackson-module-jaxb-annotations.git</connection>
    <developerConnection>scm:git:**************:FasterXML/jackson-module-jaxb-annotations.git</developerConnection>
    <url>http://github.com/FasterXML/jackson-module-jaxb-annotations</url>    
    <tag>jackson-module-jaxb-annotations-2.3.1</tag>
  </scm>

  <properties>
   <!-- Generate PackageVersion.java into this directory. -->
    <packageVersion.dir>com/fasterxml/jackson/module/jaxb</packageVersion.dir>
    <packageVersion.package>${project.groupId}.jaxb</packageVersion.package>

    <jackson.core.version>2.3.1</jackson.core.version>
    <osgi.export>
com.fasterxml.jackson.module.jaxb,
com.fasterxml.jackson.module.jaxb.deser,
com.fasterxml.jackson.module.jaxb.ser
    </osgi.export>
    <osgi.import>
javax.activation
,org.w3c.dom,
,javax.xml.parsers,
,javax.xml.bind
,javax.xml.bind.annotation
,javax.xml.bind.annotation.adapters
,com.fasterxml.jackson.annotation
,com.fasterxml.jackson.core
,com.fasterxml.jackson.core.type
,com.fasterxml.jackson.core.util
,com.fasterxml.jackson.databind
,com.fasterxml.jackson.databind.cfg
,com.fasterxml.jackson.databind.annotation
,com.fasterxml.jackson.databind.deser
,com.fasterxml.jackson.databind.deser.std
,com.fasterxml.jackson.databind.introspect
,com.fasterxml.jackson.databind.jsontype
,com.fasterxml.jackson.databind.jsontype.impl
,com.fasterxml.jackson.databind.module
,com.fasterxml.jackson.databind.node
,com.fasterxml.jackson.databind.ser
,com.fasterxml.jackson.databind.ser.std
,com.fasterxml.jackson.databind.type
,com.fasterxml.jackson.databind.util
,com.fasterxml.jackson.databind.jsonschema
,com.fasterxml.jackson.databind.jsonFormatVisitors
</osgi.import>
  </properties>

  <dependencies>
    <!-- Extends Jackson core and mapper, doesnt need annotations -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>${jackson.core.version}</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>${jackson.core.version}</version>
    </dependency>
    <!--  and actual JAXB annotations... -->
    <dependency>
      <groupId>javax.xml.bind</groupId>
      <artifactId>jaxb-api</artifactId>
      <version>2.2</version>
      <scope>provided</scope>
    </dependency>

     <!-- and for testing, JUnit (or TestNG?) is needed -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.10</version>
      <scope>test</scope>
    </dependency>
     <!-- as well as JAX-RS (jsr-311) -->
    <dependency>
      <groupId>javax.ws.rs</groupId>
      <artifactId>jsr311-api</artifactId>
      <version>1.1.1</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${surefire.version}</version>
        <configuration>
          <excludes>
            <exclude>com/fasterxml/jackson/**/failing/*.java</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <!-- Inherited from oss-base. Generate PackageVersion.java.-->
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
        <executions>
          <execution>
            <id>process-packageVersion</id>
            <phase>process-sources</phase>
          </execution>
        </executions>
      </plugin>

    </plugins>
  </build>
</project>
