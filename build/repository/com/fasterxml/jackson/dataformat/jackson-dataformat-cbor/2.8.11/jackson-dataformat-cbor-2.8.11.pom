<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion> 
  <parent>
    <groupId>com.fasterxml.jackson.dataformat</groupId>
    <artifactId>jackson-dataformats-binary</artifactId>
    <version>2.8.11</version>
  </parent>
  <artifactId>jackson-dataformat-cbor</artifactId>
  <name>Jackson dataformat: CBOR</name>
  <packaging>bundle</packaging>
  <description>Support for reading and writing Concise Binary Object Representation
([CBOR](https://www.rfc-editor.org/info/rfc7049)
encoded data using Jackson abstractions (streaming API, data binding, tree model)
  </description>
  <url>http://github.com/FasterXML/jackson-dataformats-binary</url>    

  <properties>
    <!-- Generate PackageVersion.java into this directory. -->
    <packageVersion.dir>com/fasterxml/jackson/dataformat/cbor</packageVersion.dir>
    <packageVersion.package>${project.groupId}.cbor</packageVersion.package>
    <!--
     | Default onfiguration properties for the OSGi maven-bundle-plugin work ok
    -->
  </properties>

  <dependencies>
    <!-- and for testing we need databind -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <!-- Inherited from oss-base. Generate PackageVersion.java.-->
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
        <executions>
          <execution>
            <id>process-packageVersion</id>
            <phase>generate-sources</phase>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
