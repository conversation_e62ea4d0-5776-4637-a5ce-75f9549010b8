<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion> 
  <parent>
    <groupId>com.fasterxml.jackson</groupId>
    <artifactId>jackson-parent</artifactId>
    <version>2.8</version>
  </parent>
  <groupId>com.fasterxml.jackson.dataformat</groupId>
  <artifactId>jackson-dataformats-binary</artifactId>
  <name>Jackson dataformats: Binary</name>
  <version>2.8.11</version>
  <packaging>pom</packaging>
  <description>Parent pom for Jackson binary dataformats.
  </description>

  <modules>
    <module>cbor</module>
    <module>smile</module>
    <module>avro</module>
    <module>protobuf</module>
  </modules>

  <url>https://github.com/FasterXML/jackson-dataformats-binary</url>
  <scm>
    <connection>scm:git:**************:FasterXML/jackson-dataformats-binary.git</connection>
    <developerConnection>scm:git:**************:FasterXML/jackson-dataformats-binary.git</developerConnection>
    <url>http://github.com/FasterXML/jackson-dataformats-binary</url>    
    <tag>jackson-dataformats-binary-2.8.11</tag>
  </scm>
  <issueManagement>
    <url>https://github.com/FasterXML/jackson-dataformats-binary/issues</url>
  </issueManagement>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <version.jackson.core>2.8.11</version.jackson.core>
    <version.asm>5.1</version.asm>
  </properties>

  <dependencyManagement>
    <dependencies>
      <!-- jackson-parent defines version of annotations, but we need to specify version
           for databind, either for testing (some backends) or regular dep (others)
        -->
      <dependency>
	<groupId>com.fasterxml.jackson.core</groupId>
	<artifactId>jackson-databind</artifactId>
	<version>${version.jackson.core}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <!-- all dataformats extend core so just include here -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>${version.jackson.core}</version>
    </dependency>

    <dependency> <!-- all components use junit for testing -->
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <pluginManagement>
      <plugins>
	<plugin>
	  <!-- Inherited from oss-base. Generate PackageVersion.java.-->
          <groupId>com.google.code.maven-replacer-plugin</groupId>
          <artifactId>replacer</artifactId>
	  <executions>
            <execution>
              <id>process-packageVersion</id>
              <phase>generate-sources</phase>
            </execution>
          </executions>
	</plugin>

	<plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <configuration>
            <excludes>
              <exclude>com/fasterxml/jackson/**/failing/*.java</exclude>
            </excludes>
          </configuration>
	</plugin>
      </plugins>
    </pluginManagement>
  </build>

</project>
