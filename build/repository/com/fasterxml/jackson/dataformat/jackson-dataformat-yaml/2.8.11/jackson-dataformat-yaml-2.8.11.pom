<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion> 
  <parent>
    <groupId>com.fasterxml.jackson</groupId>
    <artifactId>jackson-parent</artifactId>
    <version>2.8</version>
  </parent>

  <groupId>com.fasterxml.jackson.dataformat</groupId>
  <artifactId>jackson-dataformat-yaml</artifactId>
  <version>2.8.11</version>
  <packaging>bundle</packaging>
  <name>Jackson-dataformat-YAML</name>
  <description>Support for reading and writing YAML-encoded data via Jackson abstractions.
  </description>
  <url>https://github.com/FasterXML/jackson</url>
  <scm>
    <connection>scm:git:**************:FasterXML/jackson-dataformat-yaml.git</connection>
    <developerConnection>scm:git:**************:FasterXML/jackson-dataformat-yaml.git</developerConnection>
    <url>http://github.com/FasterXML/jackson-dataformat-yaml</url>    
    <tag>jackson-dataformat-yaml-2.8.11</tag>
  </scm>

  <properties>
    <version.jackson.core>2.8.11</version.jackson.core>

    <packageVersion.dir>com/fasterxml/jackson/dataformat/yaml</packageVersion.dir>
    <packageVersion.package>${project.groupId}.yaml</packageVersion.package>
    <!-- 18-May-2016, tatu: used to have to specify `osgi.private` to included shaded
           package; but must NOT when included as regular dependency
      -->
    <pax.url.version>1.2.8</pax.url.version>
    <pax.exam.version>4.3.0</pax.exam.version>
    <felix.version>4.0.3</felix.version>
  </properties>

  <dependencies>
    <!-- Extends Jackson core, databind (optional); uses SnakeYAML for parsing, generation -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>${version.jackson.core}</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>${version.jackson.core}</version>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.yaml</groupId>
      <artifactId>snakeyaml</artifactId>
      <version>1.17</version>
    </dependency>

     <!-- and for testing need annotations -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.ops4j.pax.exam</groupId>
      <artifactId>pax-exam-container-native</artifactId>
      <version>${pax.exam.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.ops4j.pax.exam</groupId>
      <artifactId>pax-exam-junit4</artifactId>
      <version>${pax.exam.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.ops4j.pax.exam</groupId>
      <artifactId>pax-exam-link-mvn</artifactId>
      <version>${pax.exam.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.ops4j.pax.url</groupId>
      <artifactId>pax-url-aether</artifactId>
      <version>2.2.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.felix</groupId>
      <artifactId>org.apache.felix.framework</artifactId>
      <version>${felix.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-log4j12</artifactId>
        <version>1.6.6</version>
        <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
<!-- 04-Jul-2016, tatu: Unfortunately this test fails during release, so disable for 2.8.0.
   But test itself should not been necessary as SnakeYAML not shaded in any more
   (which may cause test failure
  -->
<!--
      <plugin>
          <groupId>org.apache.servicemix.tooling</groupId>
          <artifactId>depends-maven-plugin</artifactId>
          <version>1.2</version>
          <executions>
              <execution>
                  <id>generate-depends-file</id>
                  <phase>generate-test-resources</phase>
                  <goals>
                      <goal>generate-depends-file</goal>
                  </goals>
              </execution>
          </executions>
      </plugin>
-->
      <!-- pax-exam test for OSGi has to be an IT -->
<!--

      <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-failsafe-plugin</artifactId>
          <version>2.18</version>
          <executions>
            <execution>
             <goals>
               <goal>integration-test</goal>
               <goal>verify</goal>
             </goals>
            </execution>
          </executions>
          <configuration>
              <systemPropertyVariables>
                  <project.build.directory>${project.build.directory}</project.build.directory>
                  <project.version>${project.version}</project.version>
              </systemPropertyVariables>
          </configuration>
      </plugin>
-->
      <plugin>
        <!-- Inherited from oss-base. Generate PackageVersion.java.-->
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
        <executions>
          <execution>
            <id>process-packageVersion</id>
            <phase>generate-sources</phase>
          </execution>
        </executions>
      </plugin>
      <!--  Need to skip known-failing tests for build... -->
      <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <configuration>
            <excludes>
              <exclude>com/fasterxml/jackson/dataformat/yaml/failing/*.java</exclude>
            </excludes>
            <includes>
              <include>**/*Test.java</include>
              <include>**/Test*.java</include>
            </includes>
          </configuration>
        </plugin>
    </plugins>
  </build>

</project>
