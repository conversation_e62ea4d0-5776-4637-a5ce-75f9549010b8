<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion> 
  <parent>
    <groupId>com.fasterxml.jackson.dataformat</groupId>
    <artifactId>jackson-dataformats-text</artifactId>
    <version>2.9.4</version>
  </parent>
  <artifactId>jackson-dataformat-yaml</artifactId>
  <packaging>bundle</packaging>
  <name>Jackson-dataformat-YAML</name>
  <description>Support for reading and writing YAML-encoded data via Jackson abstractions.
  </description>
  <url>https://github.com/FasterXML/jackson-dataformats-text</url>

  <properties>
    <packageVersion.dir>com/fasterxml/jackson/dataformat/yaml</packageVersion.dir>
    <packageVersion.package>${project.groupId}.yaml</packageVersion.package>
  </properties>

  <dependencies>
    <!-- Extends Jackson core, databind (optional); uses SnakeYAML for parsing, generation -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <!-- let's make it possible to avoid databind if only using streaming portion -->
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.yaml</groupId>
      <artifactId>snakeyaml</artifactId>
      <version>1.18</version>
    </dependency>

     <!-- and for testing need annotations -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <!-- Inherited from oss-base. Generate PackageVersion.java.-->
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
        <executions>
          <execution>
            <id>process-packageVersion</id>
            <phase>generate-sources</phase>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

</project>
