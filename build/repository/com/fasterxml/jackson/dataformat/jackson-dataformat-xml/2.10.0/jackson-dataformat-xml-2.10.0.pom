<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion> 
  <parent>
    <groupId>com.fasterxml.jackson</groupId>
    <artifactId>jackson-base</artifactId>
    <version>2.10.0</version>
  </parent>
  <groupId>com.fasterxml.jackson.dataformat</groupId>
  <artifactId>jackson-dataformat-xml</artifactId>
  <version>2.10.0</version>
  <name>Jackson-dataformat-XML</name>
  <packaging>bundle</packaging>
  <description>Data format extension for <PERSON> (http://jackson.codehaus.org) to offer
alternative support for serializing POJOs as XML and deserializing XML as pojos.
Support implemented on top of Stax API (javax.xml.stream), by implementing core Jackson Streaming API types like JsonGenerator, JsonParser and JsonFactory.
Some data-binding types overridden as well (ObjectMapper sub-classed as XmlMapper).
  </description>
  <url>https://github.com/FasterXML/jackson-dataformat-xml</url>
  <scm>
    <connection>scm:git:**************:FasterXML/jackson-dataformat-xml.git</connection>
    <developerConnection>scm:git:**************:FasterXML/jackson-dataformat-xml.git</developerConnection>
    <url>http://github.com/FasterXML/jackson-dataformat-xml</url>    
    <tag>jackson-dataformat-xml-2.10.0</tag>
  </scm>
  <properties>
    <packageVersion.dir>com/fasterxml/jackson/dataformat/xml</packageVersion.dir>
    <packageVersion.package>${project.groupId}.xml</packageVersion.package>

    <!-- Default export should work fine... -->
    <!-- And presumably import too? -->
  </properties>

  <dependencies>
    <!-- Extends Jackson (jackson-mapper); requires Stax API (and implementation on deploy time), Stax2 API.
         Also requires JAXB annotations module
      -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>
    <!--  JAXB annotation introspector is needed too? -->
    <dependency>
      <groupId>com.fasterxml.jackson.module</groupId>
      <artifactId>jackson-module-jaxb-annotations</artifactId>
    </dependency>
    <!-- 20-Mar-2019, tatu: Stax-api been part of JDK since Java 6, so let's drop
        dependency as it causes issues with JDK9+ module info
      -->
    <!--
    <dependency>
      <groupId>javax.xml.stream</groupId>
      <artifactId>stax-api</artifactId>
      <version>1.0-2</version>
      <scope>provided</scope>
    </dependency>
    -->

    <!--  But Stax2 API must be included -->
    <dependency>
      <groupId>org.codehaus.woodstox</groupId>
      <artifactId>stax2-api</artifactId>
      <version>4.2</version>
      <exclusions>
        <exclusion>
	  <groupId>javax.xml.stream</groupId>
	  <artifactId>stax-api</artifactId>
        </exclusion>
      </exclusions> 
    </dependency>
    <!--  and a Stax impl is needed: SJSXP (from JDK 1.6) might work, but always   
          has odd issues. Let's default to Woodstox: caller can upgrade to Aalto
         (needs to block this dep)
      -->
    <!-- 09-May-2016, tatu: With Jackson 2.8, let's make this compile-dep to make it
        less likely users accidentally try to use Sjsxp from JDK, which leads to probs
      -->
    <!-- 16-Jul-2016, tatu: For 2.10, need Woodstox 6.0 to get module info -->
    <dependency>
      <groupId>com.fasterxml.woodstox</groupId>
      <artifactId>woodstox-core</artifactId>
      <version>6.0.1</version>
      <exclusions>
        <exclusion>
	  <groupId>javax.xml.stream</groupId>
	  <artifactId>stax-api</artifactId>
        </exclusion>
      </exclusions> 
    </dependency>

    <!-- 04-Feb-2018, tatu: Finally, Java 9 does not bundle JAXB, not even API;
            at least include it for tests. May need to include actual dep to
            API for runtime too, but first for tests
      -->
    <dependency>
      <groupId>jakarta.xml.bind</groupId>
      <artifactId>jakarta.xml.bind-api</artifactId>
      <version>2.3.2</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <!-- Inherited from oss-base. Generate PackageVersion.java.-->
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
        <executions>
          <execution>
            <id>process-packageVersion</id>
            <phase>generate-sources</phase>
          </execution>
        </executions>
      </plugin>
      <!--  Need to skip known-failing tests for build... -->
      <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${version.plugin.surefire}</version>
          <configuration>
            <excludes>
              <exclude>com/fasterxml/jackson/dataformat/xml/failing/*.java</exclude>
            </excludes>
            <includes>
              <include>**/Test*.java</include>
              <include>**/*Test.java</include>
            </includes>
          </configuration>
        </plugin>
      <!-- 20-Mar-2019, tatu: use Moditect for JDK9+ module info inclusion -->
      <plugin>
	<groupId>org.moditect</groupId>
	<artifactId>moditect-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

  <!-- Alas, need to include snapshot reference since otherwise can not find
       snapshot of parent... -->
  <repositories>
    <repository>
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
      <releases><enabled>false</enabled></releases>
      <snapshots><enabled>true</enabled></snapshots>
    </repository>
  </repositories>

</project>
