<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>ch.qos.logback</groupId>
    <artifactId>logback-parent</artifactId>
    <version>1.1.2</version>
  </parent>

  <artifactId>logback-core</artifactId>
  <packaging>jar</packaging>
  <name>Logback Core Module</name>
  <description>logback-core module</description>

  <url>http://logback.qos.ch</url>

  <properties>
    <!--<scala.version>2.9.1</scala.version>-->
  </properties>

  <licenses>
    <license>
      <name>Eclipse Public License - v 1.0</name>
      <url>http://www.eclipse.org/legal/epl-v10.html</url>
    </license>

    <license>
      <name>GNU Lesser General Public License</name>
      <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
    </license>
  </licenses>

  <dependencies>
    <dependency>
       <groupId>org.codehaus.janino</groupId>
      <artifactId>janino</artifactId>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>org.fusesource.jansi</groupId>
      <artifactId>jansi</artifactId>
      <optional>true</optional>      
    </dependency>

    <dependency>
      <groupId>javax.mail</groupId>
      <artifactId>mail</artifactId>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.apache.geronimo.specs</groupId>
      <artifactId>geronimo-jms_1.1_spec</artifactId>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>1.9.0</version>
      <scope>test</scope>
    </dependency>


    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>servlet-api</artifactId>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>

    <!--<dependency>-->
      <!--<groupId>org.scala-lang</groupId>-->
      <!--<artifactId>scala-library</artifactId>-->
      <!--<version>${scala.version}</version>-->
      <!--<scope>test</scope>-->
    <!--</dependency>-->

  </dependencies>


  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${maven-surefire-plugin.version}</version>
        <configuration>
          <forkMode>once</forkMode>
          <reportFormat>plain</reportFormat>
          <!--<parallel>classes</parallel>-->
          <trimStackTrace>false</trimStackTrace>
          <excludes>
            <exclude>**/All*Test.java</exclude>
            <exclude>**/PackageTest.java</exclude>
            <!-- ConsoleAppenderTest redirects System.out which is not well tolerated by Maven -->
            <exclude>**/ConsoleAppenderTest.java</exclude>
            <!--<exclude>**/TimeBasedRollingTest.java</exclude>-->
          </excludes>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>${maven-jar-plugin.version}</version>
        <configuration>
          <archive>
            <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF
            </manifestFile>
          </archive>
        </configuration>
        <executions>
          <execution>
            <id>bundle-test-jar</id>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
              <goal>test-jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <extensions>true</extensions>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <instructions>
            <Export-Package>ch.qos.logback.core.*</Export-Package>
            <Import-Package>
              javax.*;resolution:=optional,
              org.xml.*;resolution:=optional,
              org.fusesource.jansi;resolution:=optional,
              org.codehaus.janino;resolution:=optional,
              org.codehaus.commons.compiler;resolution:=optional,
              *
            </Import-Package>
            <Bundle-RequiredExecutionEnvironment>J2SE-1.5
            </Bundle-RequiredExecutionEnvironment>
          </instructions>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>