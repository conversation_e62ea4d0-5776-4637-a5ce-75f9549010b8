<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>ch.qos.logback</groupId>
    <artifactId>logback-parent</artifactId>
    <version>1.2.9</version>
  </parent>

  <artifactId>logback-core</artifactId>
  <packaging>jar</packaging>
  <name>Logback Core Module</name>
  <description>logback-core module</description>

  <dependencies>
    <dependency>
       <groupId>org.codehaus.janino</groupId>
      <artifactId>janino</artifactId>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.fusesource.jansi</groupId>
      <artifactId>jansi</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>javax.mail</groupId>
      <artifactId>mail</artifactId>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>joda-time</groupId>
      <artifactId>joda-time</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <forkCount>1C</forkCount>
          <reuseForks>true</reuseForks>
          <parallel>classes</parallel>
          <threadCount>20</threadCount>
          <reportFormat>plain</reportFormat>
          <trimStackTrace>false</trimStackTrace>
          <excludes>
            <exclude>**/All*Test.java</exclude>
            <exclude>**/PackageTest.java</exclude>
            <!-- ConsoleAppenderTest redirects System.out which is not well tolerated by Maven -->
            <exclude>**/ConsoleAppenderTest.java</exclude>
            <!--<exclude>**/TimeBasedRollingTest.java</exclude>-->
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
          </archive>
        </configuration>
        <executions>
          <execution>
            <id>bundle-test-jar</id>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
              <goal>test-jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <extensions>true</extensions>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <instructions>
            <Export-Package>ch.qos.logback.core.*</Export-Package>
            <Import-Package>
              javax.*;resolution:=optional,
              org.xml.*;resolution:=optional,
              org.fusesource.jansi;resolution:=optional,
              org.codehaus.janino;resolution:=optional,
              org.codehaus.commons.compiler;resolution:=optional,
              *
            </Import-Package>
            <Bundle-RequiredExecutionEnvironment>JavaSE-1.6
            </Bundle-RequiredExecutionEnvironment>
          </instructions>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>