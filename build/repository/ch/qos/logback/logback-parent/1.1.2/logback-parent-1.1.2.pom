<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <groupId>ch.qos.logback</groupId>
  <artifactId>logback-parent</artifactId>
  <version>1.1.2</version>
  <packaging>pom</packaging>
  <name>Logback-Parent</name>
  <description>logback project pom.xml file</description>

  <url>http://logback.qos.ch</url>

  <organization>
    <name>QOS.ch</name>
    <url>http://www.qos.ch</url>
  </organization>
  <inceptionYear>2005</inceptionYear>

  <licenses>
    <license>
      <name>Eclipse Public License - v 1.0</name>
      <url>http://www.eclipse.org/legal/epl-v10.html</url>
    </license>

    <license>
      <name>GNU Lesser General Public License</name>
      <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
    </license>
  </licenses>

  <scm>
    <url>https://github.com/ceki/logback</url>
    <connection>**************:ceki/logback.git</connection>
  </scm>


  <modules>
    <module>logback-core</module>
    <module>logback-classic</module>
    <module>logback-access</module>
    <module>logback-site</module>
    <module>logback-examples</module>
  </modules>

  <properties>
    <!-- target JDK version == source JDK version -->
    <jdk.version>1.5</jdk.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <!-- slf4j.version property is used below, in
         logback-classic/pom.xml and in setClasspath.cmd -->
    <slf4j.version>1.7.6</slf4j.version>
    <junit.version>4.10</junit.version>
    <javax.mail.version>1.4</javax.mail.version>
    <janino.version>2.6.1</janino.version>
    <groovy.version>2.0.7</groovy.version>

    <consolePlugin.version>1.1.0</consolePlugin.version>
    <tomcat.version>7.0.21</tomcat.version>
    <jetty.version>7.5.1.v20110908</jetty.version>
    <jansi.version>1.9</jansi.version>

    <maven-compiler-plugin.version>2.3.2</maven-compiler-plugin.version>
    <maven-jar-plugin.version>2.3.1</maven-jar-plugin.version>
    <maven-surefire-plugin.version>2.14.1</maven-surefire-plugin.version>
    <maven-license-plugin.version>1.9.0</maven-license-plugin.version>
    <findbugs-maven-plugin.version>2.5</findbugs-maven-plugin.version>

  </properties>

  <developers>
    <developer>
      <id>ceki</id>
      <name>Ceki Gulcu</name>
      <email><EMAIL></email>
    </developer>

    <developer>
      <id>hixi</id>
      <name>Joern Huxhorn</name>
      <email><EMAIL></email>
    </developer>

  </developers>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>${junit.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.easytesting</groupId>
      <artifactId>fest-assert</artifactId>
      <version>1.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>


  <dependencyManagement>
    <dependencies>
      <!-- Project modules -->
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-core</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-core</artifactId>
        <version>${project.version}</version>
        <type>test-jar</type>
      </dependency>

      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-access</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.janino</groupId>
        <artifactId>janino</artifactId>
        <version>${janino.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-all</artifactId>
        <version>${groovy.version}</version>
      </dependency>
      <dependency>
        <groupId>org.fusesource.jansi</groupId>
        <artifactId>jansi</artifactId>
        <version>${jansi.version}</version>
      </dependency>

      <dependency>
        <groupId>javax.mail</groupId>
        <artifactId>mail</artifactId>
        <version>${javax.mail.version}</version>
      </dependency>
      <dependency>
        <groupId>dom4j</groupId>
        <artifactId>dom4j</artifactId>
        <version>1.6.1</version>
      </dependency>
      <dependency>
        <groupId>hsqldb</groupId>
        <artifactId>hsqldb</artifactId>
        <version>1.8.0.7</version>
      </dependency>
      <dependency>
        <groupId>com.h2database</groupId>
        <artifactId>h2</artifactId>
        <version>1.2.132</version>
      </dependency>
      <dependency>
        <groupId>postgresql</groupId>
        <artifactId>postgresql</artifactId>
        <version>8.4-701.jdbc4</version>
      </dependency>
      <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>5.1.9</version>
      </dependency>


      <dependency>
        <groupId>org.apache.tomcat</groupId>
        <artifactId>tomcat-catalina</artifactId>
        <version>${tomcat.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.tomcat</groupId>
        <artifactId>tomcat-coyote</artifactId>
        <version>${tomcat.version}</version>
      </dependency>

      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-server</artifactId>
        <version>${jetty.version}</version>
      </dependency>
      <!--
      <dependency>
        <groupId>org.mortbay.jetty</groupId>
        <artifactId>servlet-api-2.5</artifactId>
        <version>6.1.1</version>
      </dependency>
      -->
      <dependency>
        <groupId>org.apache.geronimo.specs</groupId>
        <artifactId>geronimo-jms_1.1_spec</artifactId>
        <version>1.0</version>
      </dependency>
      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>servlet-api</artifactId>
        <version>2.5</version>
      </dependency>
    </dependencies>
  </dependencyManagement>


  <build>
    <extensions>
      <extension>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-ssh</artifactId>
        <version>2.0</version>
      </extension>
    </extensions>

    <plugins>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven-compiler-plugin.version}</version>
        <configuration>
          <source>${jdk.version}</source>
          <target>${jdk.version}</target>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${maven-surefire-plugin.version}</version>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>2.1.2</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>${maven-jar-plugin.version}</version>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-eclipse-plugin</artifactId>
        <version>2.8</version>
        <configuration>
          <downloadSources>true</downloadSources>
          <downloadJavadocs>true</downloadJavadocs>
          <sourceIncludes>
            <sourceInclude>**/*.java</sourceInclude>
          </sourceIncludes>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-assembly-plugin</artifactId>
        <version>2.1</version>
        <configuration>
          <descriptors>
            <descriptor>src/main/assembly/dist.xml</descriptor>
          </descriptors>
          <finalName>logback-${project.version}</finalName>
          <appendAssemblyId>false</appendAssemblyId>
          <outputDirectory>target/site/dist/</outputDirectory>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <!-- avoid "Duplicate entry" warnings -->
        <version>2.1.0</version>
      </plugin>

      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <version>${findbugs-maven-plugin.version}</version>
        <configuration>
          <threshold>High</threshold>
          <!--<trace>true</trace>-->
          <excludeFilterFile>findbugs-exclude.xml</excludeFilterFile>
        </configuration>
      </plugin>

      <!-- ================ site plugin ==================== -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <version>3.0</version>
        <configuration>
          <reportPlugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-jxr-plugin</artifactId>
              <version>2.3</version>
              <configuration>
                <aggregate>true</aggregate>
                <javadocDir>target/site/apidocs/</javadocDir>
                <linkJavadoc>true</linkJavadoc>
              </configuration>
            </plugin>

            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-javadoc-plugin</artifactId>
              <version>2.8</version>
              <configuration>
                <aggregate>true</aggregate>
                <!--<linksource>true</linksource>-->
                <links>
                  <link>
                    http://java.sun.com/j2se/1.5.0/docs/api
                  </link>
                </links>
                <groups>
                  <group>
                    <title>Logback Core</title>
                    <packages>ch.qos.logback.core:ch.qos.logback.core.*
                    </packages>
                  </group>
                  <group>
                    <title>Logback Classic</title>
                    <packages>
                      ch.qos.logback:ch.qos.logback.classic:ch.qos.logback.classic.*
                    </packages>
                  </group>
                  <group>
                    <title>Logback Access</title>
                    <packages>ch.qos.logback.access:ch.qos.logback.access.*
                    </packages>
                  </group>
                  <group>
                    <title>SLF4J</title>
                    <packages>org.slf4j:org.slf4j.*</packages>
                  </group>
                  <group>
                    <title>Examples</title>
                    <packages>chapter*:joran*</packages>
                  </group>
                </groups>
              </configuration>
            </plugin>

          </reportPlugins>
        </configuration>
      </plugin>

    </plugins>
  </build>

  <distributionManagement>
    <site>
      <id>pixie</id>
      <url>scp://pixie.qos.ch/var/www/logback.qos.ch/htdocs/</url>
    </site>

    <repository>
      <id>sonatype-nexus-staging</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>

      <!--<id>pixie</id>-->
      <!--<url>scp://pixie.qos.ch/var/mvnrepo/</url>-->
    </repository>

  </distributionManagement>


  <profiles>
    <profile>
      <id>testSkip</id>
      <properties>
        <maven.test.skip>true</maven.test.skip>
      </properties>
    </profile>
    <profile>
      <id>license</id>
      <build>
        <plugins>
          <plugin>
            <groupId>com.mycila.maven-license-plugin</groupId>
            <artifactId>maven-license-plugin</artifactId>
            <version>1.9.0</version>
            <configuration>
              <header>src/main/licenseHeader.txt</header>
              <quiet>false</quiet>
              <failIfMissing>true</failIfMissing>
              <aggregate>true</aggregate>
              <includes>
                <include>src/**/*.java</include>
              </includes>
              <useDefaultExcludes>true</useDefaultExcludes>
              <useDefaultMapping>true</useDefaultMapping>
              <properties>
                <year>1999</year>
              </properties>
              <headerDefinitions>
                <headerDefinition>src/main/javadocHeaders.xml</headerDefinition>
              </headerDefinitions>
            </configuration>
          </plugin>
        </plugins>
      </build>

    </profile>

    <profile>
      <id>javadocjar</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>2.8</version>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>


    <profile>
      <id>sign-artifacts</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>1.1</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>


  </profiles>

</project>
