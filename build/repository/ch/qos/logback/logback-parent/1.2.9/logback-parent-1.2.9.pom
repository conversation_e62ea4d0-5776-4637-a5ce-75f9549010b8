<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <groupId>ch.qos.logback</groupId>
  <artifactId>logback-parent</artifactId>
  <version>1.2.9</version>
  <packaging>pom</packaging>

  <name>Logback-Parent</name>
  <description>logback project pom.xml file</description>

  <url>http://logback.qos.ch</url>

  <organization>
    <name>QOS.ch</name>
    <url>http://www.qos.ch</url>
  </organization>
  <inceptionYear>2005</inceptionYear>

  <licenses>
    <license>
      <name>Eclipse Public License - v 1.0</name>
      <url>http://www.eclipse.org/legal/epl-v10.html</url>
    </license>

    <license>
      <name>GNU Lesser General Public License</name>
      <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
    </license>
  </licenses>

  <scm>
    <url>https://github.com/ceki/logback</url>
    <connection>scm:**************:qos-ch/logback.git</connection>
  </scm>

  <modules>
    <module>logback-core</module>
    <module>logback-classic</module>
    <module>logback-access</module>
    <module>logback-site</module>
    <module>logback-examples</module>
  </modules>

  <properties>
    <maven.compiler.source>1.6</maven.compiler.source>
    <maven.compiler.target>1.6</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <junit.version>4.10</junit.version>
    <javax.mail.version>1.4</javax.mail.version>
    <janino.version>3.0.6</janino.version>
    <!-- slf4j.version property is used below, in
         logback-classic/pom.xml and in setClasspath.cmd 
    -->    
    <slf4j.version>1.7.32</slf4j.version>
    <cal10n.version>0.8.1</cal10n.version>
    <consolePlugin.version>1.1.0</consolePlugin.version>
    <!--<tomcat.version>7.0.59</tomcat.version>-->
    <tomcat.version>8.5.9</tomcat.version>
    <!--<jetty.version>7.5.1.v20110908</jetty.version>-->
    <jetty.version>8.2.0.v20160908</jetty.version>
    <!--<jetty.version>9.2.9.v20150224</jetty.version>-->
    <jansi.version>1.9</jansi.version>
    <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
    <maven-surefire-plugin.version>2.19.1</maven-surefire-plugin.version>    
    <maven-site-plugin.version>3.7.1</maven-site-plugin.version>
    <maven-javadoc-plugin.version>3.1.0</maven-javadoc-plugin.version>
    <maven-source-plugin.version>3.2.0</maven-source-plugin.version>
    <maven-license-plugin.version>3.0</maven-license-plugin.version>
    <maven-jar-plugin.version>2.6</maven-jar-plugin.version>
    <maven-jxr-plugin.version>3.1.1</maven-jxr-plugin.version>
    <maven-deploy-plugin.version>3.0.0-M1</maven-deploy-plugin.version>
    <cobertura.maven.plugin.version>2.7</cobertura.maven.plugin.version>

  </properties>

  <developers>
    <developer>
      <id>ceki</id>
      <name>Ceki Gulcu</name>
      <email><EMAIL></email>
    </developer>

    <developer>
      <id>hixi</id>
      <name>Joern Huxhorn</name>
      <email><EMAIL></email>
    </developer>
  </developers>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>${junit.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>1.7.1</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <dependencyManagement>
    <dependencies>
      <!-- start of dependencies duplicated from logback-bom/pom.xml -->
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-core</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-access</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-core</artifactId>
        <version>${project.version}</version>
        <type>test-jar</type>
      </dependency>
      <!-- end of dependencies duplicated from logback-bom/pom.xml -->
    
      <dependency>
        <groupId>org.codehaus.janino</groupId>
        <artifactId>janino</artifactId>
        <version>${janino.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy</artifactId>
        <version>${groovy.version}</version>
      </dependency>

      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-test</artifactId>
        <version>${groovy.version}</version>
      </dependency>
      
      <dependency>
        <groupId>org.fusesource.jansi</groupId>
        <artifactId>jansi</artifactId>
        <version>${jansi.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.mail</groupId>
        <artifactId>mail</artifactId>
        <version>${javax.mail.version}</version>
      </dependency>
      <dependency>
        <groupId>dom4j</groupId>
        <artifactId>dom4j</artifactId>
        <version>1.6.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat</groupId>
        <artifactId>tomcat-catalina</artifactId>
        <version>${tomcat.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat</groupId>
        <artifactId>tomcat-coyote</artifactId>
        <version>${tomcat.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-server</artifactId>
        <version>${jetty.version}</version>
      </dependency>

      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>javax.servlet-api</artifactId>
        <version>3.1.0</version>
      </dependency>

      <dependency>
        <groupId>joda-time</groupId>
        <artifactId>joda-time</artifactId>
        <version>2.9.2</version>
      </dependency>

      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>2.7.9</version>
      </dependency>
    </dependencies>
  </dependencyManagement>


  <build>
    <extensions>
      <extension>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-ssh</artifactId>
        <version>2.10</version>
      </extension>
    </extensions>

    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>3.0.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${maven-compiler-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>${maven-deploy-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>2.5.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>3.0.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>${maven-site-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven-surefire-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>${maven-jar-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${maven-javadoc-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>2.10</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.5.3</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>${maven-source-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>findbugs-maven-plugin</artifactId>
          <version>3.0.4</version>
        </plugin>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>2.5.4</version>
        </plugin>

        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-maven-plugin</artifactId>
          <version>1.15</version>
          <configuration>
            <ignores>
              <ignore>sun.reflect.Reflection</ignore>
            </ignores>
            <signature>
              <groupId>org.codehaus.mojo.signature</groupId>
              <artifactId>java16</artifactId>
              <version>1.0</version>
            </signature>
          </configuration>
        </plugin>

      </plugins>

    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar</goal>
              <goal>test-jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-assembly-plugin</artifactId>
        <version>3.0.0</version>
        <configuration>
          <descriptors>
            <descriptor>src/main/assembly/dist.xml</descriptor>
          </descriptors>
          <finalName>logback-${project.version}</finalName>
          <appendAssemblyId>false</appendAssemblyId>
          <outputDirectory>target/site/dist/</outputDirectory>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <configuration>
          <threshold>High</threshold>
          <!--<trace>true</trace>-->
          <excludeFilterFile>findbugs-exclude.xml</excludeFilterFile>
        </configuration>
      </plugin>

 


      <!-- ================ site plugin ==================== -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <configuration>
          <reportPlugins>

            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-jxr-plugin</artifactId>
              <version>${maven-jxr-plugin.version}</version>
              <configuration>
                <aggregate>true</aggregate>
                <javadocDir>target/site/apidocs/</javadocDir>
                <linkJavadoc>true</linkJavadoc>
              </configuration>
            </plugin>
            
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-javadoc-plugin</artifactId>
              <version>${maven-javadoc-plugin.version}</version>
              <configuration>
                <aggregate>true</aggregate>
                <linksource>true</linksource>
                <additionalOptions>
                  <additionalOption>-Xdoclint:none</additionalOption>
                  <doclint>none</doclint>
                </additionalOptions>
                <links>
                  <link>
                    http://docs.oracle.com/javase/6/docs/api/
                  </link>
                </links>
                <groups>
                  <group>
                    <title>Logback Core</title>
                    <packages>ch.qos.logback.core:ch.qos.logback.core.*
                    </packages>
                  </group>
                  <group>
                    <title>Logback Classic</title>
                    <packages>
                      ch.qos.logback:ch.qos.logback.classic:ch.qos.logback.classic.*
                    </packages>
                  </group>
                  <group>
                    <title>Logback Access</title>
                    <packages>ch.qos.logback.access:ch.qos.logback.access.*
                    </packages>
                  </group>
                  <group>
                    <title>SLF4J</title>
                    <packages>org.slf4j:org.slf4j.*</packages>
                  </group>
                  <group>
                    <title>Examples</title>
                    <packages>chapter*:joran*</packages>
                  </group>
                </groups>
              </configuration>
            </plugin>

          </reportPlugins>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <distributionManagement>

    <site>
      <id>qos_ch</id>
      <url>scp://te.qos.ch/var/www/logback.qos.ch/htdocs/</url>
    </site>

    <repository>
      <id>sonatype-nexus-staging</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>

  </distributionManagement>

  <profiles>
    <profile>
      <id>testSkip</id>
      <properties>
        <maven.test.skip>true</maven.test.skip>
      </properties>
    </profile>
    <profile>
      <id>license</id>
      <build>
        <plugins>
          <plugin>
            <groupId>com.mycila</groupId>
            <artifactId>license-maven-plugin</artifactId>
            <version>${maven-license-plugin.version}</version>
            <configuration>
              <header>src/main/licenseHeader.txt</header>
              <quiet>false</quiet>
              <failIfMissing>true</failIfMissing>
              <aggregate>true</aggregate>
              <includes>
                <include>src/**/*.java</include>
                <include>src/**/*.groovy</include>
              </includes>
              <useDefaultExcludes>true</useDefaultExcludes>
              <useDefaultMapping>true</useDefaultMapping>
              <properties>
                <year>1999</year>
              </properties>
              <headerDefinitions>
                <headerDefinition>src/main/javadocHeaders.xml</headerDefinition>
              </headerDefinitions>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>javadocjar</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>${maven-javadoc-plugin.version}</version>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                  <goal>test-jar</goal>
                </goals>
              </execution>
            </executions>
             <configuration>
               <additionalparam>-Xdoclint:none</additionalparam>
               <doclint>none</doclint>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>sign-artifacts</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>1.6</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>cobertura</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-site-plugin</artifactId>
            <configuration>
              <reportPlugins>
                
                
            
                <plugin>
                  <groupId>org.codehaus.mojo</groupId>
                  <artifactId>cobertura-maven-plugin</artifactId>
                  <version>${cobertura.maven.plugin.version}</version>
                  <configuration>
                    <formats>
                      <format>html</format>
                    </formats>
                    <aggregate>true</aggregate>
                  </configuration>
                </plugin>
              </reportPlugins>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>


  </profiles>

</project>
