<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                        http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <artifactId>asm-parent</artifactId>
  <groupId>asm</groupId>
  <version>3.2</version>
  <packaging>pom</packaging>

  <name>ASM</name>
  <description>A very small and fast Java bytecode manipulation framework</description>
  <url>http://asm.objectweb.org/</url>
  
  <organization>
    <name>ObjectWeb</name>
    <url>http://www.objectweb.org/</url>
  </organization>
  <inceptionYear>2000</inceptionYear>
  
  <licenses>
    <license>
      <name>BSD</name>
      <url>http://asm.objectweb.org/license.html</url>
    </license>
  </licenses>

  <developers>
    <developer>
      <name><PERSON></name>
      <id>ebruneton</id>
      <email><EMAIL></email>
      <roles>
        <role>Creator</role>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Eugene Kuleshov</name>
      <id>eu</id>
      <email><EMAIL></email>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Remi Forax</name>
      <id>forax</id>
      <email><EMAIL></email>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
  </developers>

  <scm>
    <connection>scm:svn:svn://svn.forge.objectweb.org/svnroot/asm/tags/ASM_3_2</connection>
    <developerConnection>scm:svn:svn+ssh://${maven.username}@svn.forge.objectweb.org/svnroot/asm/tags/ASM_3_2</developerConnection>
    <url>http://svn.forge.objectweb.org/cgi-bin/viewcvs.cgi/asm/tags/ASM_3_2/</url>
  </scm>
  
  <issueManagement>
    <url>http://forge.objectweb.org/tracker/?group_id=23</url>
  </issueManagement>

  <dependencyManagement>
    <dependencies>

      <dependency>
        <artifactId>asm</artifactId>
        <groupId>${project.groupId}</groupId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <artifactId>asm-tree</artifactId>
        <groupId>${project.groupId}</groupId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <artifactId>asm-analysis</artifactId>
        <groupId>${project.groupId}</groupId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <artifactId>asm-commons</artifactId>
        <groupId>${project.groupId}</groupId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <artifactId>asm-util</artifactId>
        <groupId>${project.groupId}</groupId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <artifactId>asm-xml</artifactId>
        <groupId>${project.groupId}</groupId>
        <version>${project.version}</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <mailingLists>
    <mailingList>
      <name>ASM Users List</name>
      <subscribe><EMAIL>?subject=subscribe%20asm</subscribe>
      <unsubscribe><EMAIL>?subject=unsubscribe%20asm</unsubscribe>
      <post><EMAIL></post>
      <archive>http://www.ow2.org/wws/arc/asm</archive>
    </mailingList>
    <mailingList>
      <name>ASM Team List</name>
      <subscribe><EMAIL>?subject=subscribe%20asm-team</subscribe>
      <unsubscribe><EMAIL>?subject=unsubscribe%20asm-team</unsubscribe>
      <post><EMAIL></post>
      <archive>http://www.ow2.org/wws/arc/asm-team</archive>
    </mailingList>
  </mailingLists>

  <distributionManagement>
    <downloadUrl>http://mojo.codehaus.org/my-project</downloadUrl>
    <repository>
      <id>objectweb</id>
      <uniqueVersion>false</uniqueVersion>
      <name>ObjectWeb Maven 2.0 Repository</name>
      <url>dav:https://maven.forge.objectweb.org:8002/maven2/</url>
      <layout>default</layout>
    </repository>
    <snapshotRepository>
      <id>objectweb.snapshots</id>
      <uniqueVersion>false</uniqueVersion>
      <name>ObjectWeb Maven 2.0 Snapshot Repository</name>
      <url>dav:https://maven.forge.objectweb.org:8002/maven2-snapshot/</url>
      <layout>default</layout>
    </snapshotRepository>
  </distributionManagement>

</project>
