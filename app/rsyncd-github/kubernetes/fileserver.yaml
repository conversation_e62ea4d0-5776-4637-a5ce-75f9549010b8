kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: fileserver
  namespace: admin-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 0300Gi
  storageClassName: standard-rwo
  volumeMode: Filesystem
---
kind: ConfigMap
apiVersion: v1
metadata:
  name: fileserver
  namespace: admin-system
  annotations:
    kubesphere.io/creator: admin
data:
  start.sh: >-
    mkdir -p /data/code/tapdata /data/code/tapdata-enterprise
    /data/code/tapdata-enterprise-web /data/code/tapdata-cloud \

    /data/build-temp /data/frontend /data/m2 /data/scripts /data/tar-gz
    /data/enterprise-code/ /data/enterprise-code/tapdata/ \

    /data/enterprise-code/tapdata-enterprise/
    /data/enterprise-code/tapdata-enterprise-web/ /data/enterprise-artifact/
    /data/enterprise-artifact/docker-gz/ \

    /data/enterprise-artifact/gz/


    apk add apache2-utils


    htpasswd -b -c /.htpasswd $username $password


    chmod -R 0755 /data

    rsync --daemon --config=/etc/rsync.conf

    droppy start -c /config -f /data/enterprise-artifact/
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app: fileserver
  name: fileserver
  namespace: admin-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: fileserver
  template:
    metadata:
      labels:
        app: fileserver
    spec:
      volumes:
        - name: volume-fileserver-config
          configMap:
            name: fileserver
            items:
              - key: start.sh
                path: start.sh
            defaultMode: 420
        - name: volume-fileserver-pvc
          persistentVolumeClaim:
            claimName: fileserver
      containers:
      - image: asia-docker.pkg.dev/crypto-reality-377106/tapdata/fileserver:0.3
        imagePullPolicy: IfNotPresent
        name: fileserver
        command:
        - sh
        args:
        - /root/start.sh
        ports:
        - containerPort: 80
          name: http
          protocol: TCP
        - containerPort: 873
          name: rsync
          protocol: TCP
        resources:
          limits:
            cpu: "2"
            memory: 4Gi
          requests:
            cpu: "2"
            memory: 4Gi
        env:
          - name: username
            value: tapdata
          - name: password
            value: Gotapd8!
        livenessProbe:
          tcpSocket:
            port: 8989
          initialDelaySeconds: 10
          timeoutSeconds: 1
          periodSeconds: 10
          successThreshold: 1
          failureThreshold: 3
        readinessProbe:
          tcpSocket:
            port: 8989
          initialDelaySeconds: 10
          timeoutSeconds: 1
          periodSeconds: 10
          successThreshold: 1
          failureThreshold: 3
        startupProbe:
          tcpSocket:
            port: 8989
          initialDelaySeconds: 10
          timeoutSeconds: 1
          periodSeconds: 10
          successThreshold: 1
          failureThreshold: 3
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - name: volume-fileserver-config
          readOnly: true
          mountPath: /root/
        - name: volume-fileserver-pvc
          mountPath: /data
---
apiVersion: v1
kind: Service
metadata:
  name: fileserver
  namespace: admin-system
  annotations:
    cloud.google.com/l4-rbs: "enabled"
  labels:
    app: fileserver
spec:
  type: LoadBalancer
  externalTrafficPolicy: Cluster
  ports:
    - port: 80
      targetPort: 8989
      protocol: TCP
      name: droppy
    - port: 873
      targetPort: 873
      protocol: TCP
      name: rsync
  selector:
      app: fileserver
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: fileserver
  namespace: admin-system
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: stable-db
  labels:
    app: fileserver
spec:
  rules:
    - host: fileserver.tapdata.io
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: fileserver
                port:
                  number: 80