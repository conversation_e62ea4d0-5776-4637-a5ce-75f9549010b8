mkdir -p /data/code/tapdata /data/code/tapdata-enterprise /data/code/tapdata-enterprise-web /data/code/tapdata-cloud \
/data/build-temp /data/frontend /data/m2 /data/scripts /data/tar-gz /data/enterprise-code/ /data/enterprise-code/tapdata/ \
/data/enterprise-code/tapdata-enterprise/ /data/enterprise-code/tapdata-enterprise-web/ /data/enterprise-artifact/ /data/enterprise-artifact/docker-gz/ \
/data/enterprise-artifact/gz/

apk add apache2-utils

htpasswd -b -c /.htpasswd $username $password

chmod -R 0755 /data
rsync --daemon --config=/etc/rsync.conf
droppy start -c /config -f /data/enterprise-artifact/