version: "3"
services:
  fileserver:
    container_name: fileserver
    image: tapdata-docker.pkg.coding.net/tapdata/tapdata/fileserver:1.0
    restart: always
    ports:
      - "873:873"
      - "5244:5244"
    volumes:
      - /data/fileserver/data:/data
      - /data/fileserver/config:/opt/alist/data
      - /app/
    environment:
      - PUID=0
      - PGID=0
      - UMASK=022
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 1G
        reservations:
          cpus: "0.5"
          memory: 1G