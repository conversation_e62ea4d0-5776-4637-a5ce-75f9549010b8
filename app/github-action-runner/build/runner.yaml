kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: action-runner-workdir
  namespace: action-runner
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 100Gi
  storageClassName: enterprise-rwx
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: action-runner
  name: action-runner
  namespace: action-runner
spec:
  replicas: 2
  selector:
    matchLabels:
      app: action-runner
  template:
    metadata:
      labels:
        app: action-runner
    spec:
      volumes:
        - name: workdir
          persistentVolumeClaim:
            claimName: action-runner-workdir
      containers:
        - name: action-runner
          image: asia-docker.pkg.dev/crypto-reality-377106/tapdata/github-action-runner:2.2
          securityContext:
            privileged: true
          env:
            - name: server
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: ACCESS_TOKEN
              value: ****************************************
            - name: RUNNER_SCOPE
              value: tapdata-enterprise
            - name: ORG_NAME
              value: tapdata
            - name: LABELS
              value: gcp-docker
            - name: RUNNER_TOKEN
              value: ****************************************
            - name: REPO_URL
              value: 'https://github.com/tapdata/tapdata-enterprise'
            - name: RUNNER_NAME_PREFIX
              value: gcp-docker
            - name: RUNNER_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
          resources:
            limits:
              cpu: 2
              memory: 6Gi
              ephemeral-storage: "10Gi"
            requests:
              cpu: 2
              memory: 6Gi
              ephemeral-storage: "10Gi"
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: Always
          volumeMounts:
            - name: workdir
              mountPath: /_work
            - name: workdir
              mountPath: /action-runner
