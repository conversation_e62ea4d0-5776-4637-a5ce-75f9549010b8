FROM gcr.io/kaniko-project/executor As kaniko

FROM myoung34/github-runner:latest

# 安装必要软件
RUN apt-get update -y && \
    rm /etc/localtime && \
    echo 'Asia/Shanghai' > /etc/localtime && \
    echo 'Asia/Shanghai' > /etc/timezone && \
    apt-get install wget maven vim libc6-i386 -y && \
    mkdir /usr/java

# 安装oracle jdk 11, 17
RUN wget 'http://58.251.34.123:5244/d/tools/java-17.tar.gz?sign=tKZO5QQTne90dRexZUoio6WNygJ_di_CScTa3wFuInY=:0' -O java-17.tar.gz && \
    tar -xzf java-17.tar.gz -C /usr/java && \
    rm -f java-17.tar.gz && \
    update-alternatives --install "/usr/bin/java" "java" "/usr/java/jdk-17.0.12/bin/java" 1 && \
    update-alternatives --install "/usr/bin/javac" "javac" "/usr/java/jdk-17.0.12/bin/javac" 1 && \
    update-alternatives --install "/usr/bin/jar" "jar" "/usr/java/jdk-17.0.12/bin/jar" 1 && \
    wget 'http://58.251.34.123:5244/d/tools/java-11.tar.gz?sign=HuOuaQsPU_iyMSz9ctcPRscFPBbdcSENIGkJEPlt1dg=:0' -O java-11.tar.gz && \
    tar -xzf java-11.tar.gz -C /usr/java && \
    rm -f java-11.tar.gz && \
    update-alternatives --install "/usr/bin/java" "java" "/usr/java/jdk-11.0.25/bin/java" 1 && \
    update-alternatives --install "/usr/bin/javac" "javac" "/usr/java/jdk-11.0.25/bin/javac" 1 && \
    update-alternatives --install "/usr/bin/jar" "jar" "/usr/java/jdk-11.0.25/bin/jar" 1

# 安装oracle jdk 1.8
COPY java-8.tar.gz /usr/java/java-8.tar.gz
RUN tar -xvf /usr/java/java-8.tar.gz -C /usr/java && \
    rm -rf /usr/java/java-8.tar.gz && \
    update-alternatives --install "/usr/bin/java" "java" "/usr/java/jdk1.8.0_361/bin/java" 1 && \
    update-alternatives --set java /usr/java/jdk1.8.0_361/bin/java && \
    update-alternatives --install "/usr/bin/javac" "javac" "/usr/java/jdk1.8.0_361/bin/javac" 1 && \
    update-alternatives --set javac /usr/java/jdk1.8.0_361/bin/javac && \
    update-alternatives --install "/usr/bin/jar" "jar" "/usr/java/jdk1.8.0_361/bin/jar" 1 && \
    update-alternatives --set jar /usr/java/jdk1.8.0_361/bin/jar && \
    echo 'export PATH="/usr/java/jdk1.8.0_361/bin:$PATH"' >> /etc/profile && \
    echo "export JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF8" >> /etc/profile && \
    apt-get install locales unixodbc -y && locale-gen en_US.UTF-8 && echo "export LC_ALL=en_US.UTF-8" >> /etc/profile && \
    echo "export JAVA_HOME=/usr/java/jdk1.8.0_361/" >> /etc/profile
# 安装nodejs 16、pnpm、poetry、git
RUN curl -sL https://deb.nodesource.com/setup_16.x | sudo -E bash - && \
    apt-get install -y nodejs git && \
    npm i pnpm@7.30.5 -g && \
    npm install --global yarn && \
    pip3 install poetry
# 安装kubectl
RUN curl -LO "https://dl.k8s.io/release/v1.19.0/bin/linux/amd64/kubectl" && \
    curl -LO "https://dl.k8s.io/v1.19.0/bin/linux/amd64/kubectl.sha256" && \
    echo "$(cat kubectl.sha256) kubectl" | sha256sum --check && \
    install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
# 安装gke-gcloud-auth-plugin
RUN echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | tee -a /etc/apt/sources.list.d/google-cloud-sdk.list && \
    curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo apt-key --keyring /usr/share/keyrings/cloud.google.gpg add - && \
    apt-get update && apt-get install -y google-cloud-cli && \
    apt-get install -y google-cloud-cli-gke-gcloud-auth-plugin

COPY root/.kube/config /root/.kube/config
COPY usr/local/bin/cci-iam-authenticator /usr/local/bin/cci-iam-authenticator
COPY root/keyfile.json /root/keyfile.json
COPY root/.ssh/ /root/.ssh/

# 设置权限
RUN chmod 700 /root/.ssh && \
    chmod 600 /root/.ssh/id_rsa

# gcloud登录验证
RUN gcloud auth login --cred-file='/root/keyfile.json' --project crypto-reality-377106 -q


RUN chmod +x /usr/local/bin/cci-iam-authenticator

# 设置环境变量
RUN echo 'export PATH=/usr/local/sonar/bin:$PATH' >> /etc/profile

COPY usr/local/sonar/ /usr/local/sonar/

COPY --from=kaniko /kaniko /kaniko
