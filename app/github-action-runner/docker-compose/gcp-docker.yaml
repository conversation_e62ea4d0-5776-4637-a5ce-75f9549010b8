version: "3"
services:
  build-runner:
    image: tapdata-docker.pkg.coding.net/tapdata/tapdata/github-action-runner:3.0
    privileged: true
    environment:
      - ACCESS_TOKEN=****************************************
      - RUNNER_SCOPE=tapdata-enterprise
      - RANDOM_RUNNER_SUFFIX=false
      - ORG_NAME=tapdata
      - LABELS=office-build
      - RUNNER_TOKEN=****************************************
      - REPO_URL=https://github.com/tapdata/tapdata-enterprise
      - RUNNER_NAME_PREFIX=office-build-op
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:rw
    deploy:
      replicas: 5
      resources:
        limits:
          cpus: "4"
          memory: 8G
        reservations:
          cpus: "2"
          memory: 4G