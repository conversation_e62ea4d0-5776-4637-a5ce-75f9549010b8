version: "3.1"
services:
  github-action-runner:
    image: harbor.internal.tapdata.io/tapdata/github-action-runner:3.4
    privileged: true
    environment:
      - ACCESS_TOKEN=****************************************
      - RUNNER_SCOPE=tapdata
      - RANDOM_RUNNER_SUFFIX=false
      - ORG_NAME=tapdata
      - LABELS=office-scan
      - RUNNER_TOKEN=****************************************
      - REPO_URL=https://github.com/tapdata/tapdata-connectors
      - RUNNER_NAME_PREFIX=office-scan
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:rw
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: "4"
          memory: 8G
        reservations:
          cpus: "2"
          memory: 4G