apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: gce
    kubernetes.io/ingress.global-static-ip-name: tapdata-monitor
  labels:
    app: grafana
  name: grafana
  namespace: monitor
spec:
  tls:
  - secretName: ssl-secret-monitor
  rules:
    - host: monitor.cloud.tapdata.io
      http:
        paths:
        - path: /*
          pathType: ImplementationSpecific
          backend:
            service:
              name: grafana
              port:
                number: 3000
