kind: Deployment
apiVersion: apps/v1
metadata:
  name: tapdata-v151
  namespace: dev
  labels:
    app: tapdata-v151
  annotations:
    deployment.kubernetes.io/revision: '1'
    kubesphere.io/creator: admin
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tapdata-v151
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: tapdata-v151
    spec:
      containers:
        - name: container-0weimy
          image: 'dockerhub.qingcloud.com/tapdata/tapdata:v1-0.1'
          ports:
            - name: http-0
              containerPort: 3030
              protocol: TCP
            - name: http-1
              containerPort: 3080
              protocol: TCP
          env:
            - name: MONGODB_CONNECTION_STRING
              value: '192.168.100.7:30406/tapdata-v151?authSource=admin'
            - name: MONGODB_PASSWORD
              value: Gotapd8!
            - name: MONGODB_USER
              value: root
            - name: FRONTEND_WORKER_COUNT
              value: '1'
            - name: API_WORKER_COUNT
              value: '1'
            - name: DOWNLOAD_URI
              value: 'http://139.198.127.226:30385/$/jYFba'
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      serviceAccountName: default
      serviceAccount: default
      securityContext: {}
      imagePullSecrets:
        - name: qingcloud-repo
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
