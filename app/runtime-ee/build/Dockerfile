FROM ghcr.io/tapdata/mongodb:4.2.20
RUN mkdir -p /tapdata/data /tapdata/apps/manager /tapdata/apps/iengine
RUN apt-get update -y && \
    apt-get install software-properties-common libc6-i386 -y && \
    add-apt-repository ppa:deadsnakes/ppa -y && \
    apt-get install locales -y && \
    apt-get install curl -y && \
    echo 'Asia/Shanghai' > /etc/localtime && \
    echo 'Asia/Shanghai' > /etc/timezone && \
    apt-get install wget -y
RUN wget "http://139.198.127.226:30385/$/CWe37" -O java-8.tar.gz && \
    mkdir /usr/java && \
    tar -xf java-8.tar.gz -C /usr/java && \
    rm -rf java-8.tar.gz && \
    update-alternatives --install "/usr/bin/java" "java" "/usr/java/jdk1.8.0_361/bin/java" 1 && \
    update-alternatives --set java /usr/java/jdk1.8.0_361/bin/java && \
    echo 'export PATH="/usr/java/jdk1.8.0_361/bin:$PATH"' >> /etc/profile && \
    locale-gen en_US.UTF-8 && \
    echo "export LC_ALL=en_US.UTF-8" >> /etc/profile && \
    cd /usr/bin
RUN mkdir -p /root/.m2/repository
RUN echo "export JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF8" >> /etc/profile && . /etc/profile
