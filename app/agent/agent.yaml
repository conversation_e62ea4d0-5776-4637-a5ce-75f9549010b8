kind: Pod
apiVersion: v1
metadata:
  name: agent-berry
  labels:
    app: agent-berry
  namespace: agent-test
spec:
  containers:
    - name: agent-berry
      image: tapdata-docker.pkg.coding.net/dfs/flow-engine/dfs-flow-engine:latest
      imagePullPolicy: Always
      command:
        - /opt/agent/tapdata
        - start
        - backend
        - --token
        - 'a/HZzXh5MDbwPGd8hCzZYYF0XXgDZ287oY34Sx3QAq5Z7zikkMRcI62kZHXq8RRJj6VrJcSY6ehw4iM8d8LW1WfvMS1fL2pd2cJxn++JiypcTTLyZMqXe9FJt23gtbvHxfxnfN1J1MzJl4FzSyeh0aR+SVj/hg6jmCwCseJLPCIk1ruH+bSSYBZx0d0fsU9wyR809Dc/R7NAUCfJAn2twjeWiW1PjyopaiRWP40q+ziH87T8C1YfuKYJl8ysW+bnJgLmFZsneM9tEc9dwubS9qdcM9FjthQ6MH2Zerr1vBrJOh/q37agmAcI0SPRrrogajr3taf3NUEEQOg2HqcUDDySGQUvSIgocyBtFba8cpb1bGb7vNCjJtmsykiBHnkADjKZngXAkmU2DCcBrxHXJZ71ijEAx4zrkbb3wa2EBM9MeyK5kV5bEiTrmjZJGD92pPiMjLRVilIKuFh+KWL/DQIG5UmtLqCUUafwzeU27sNqUAqc+V1sN2c4Coc8fz1rs0tEBDzbjyupJwPKP2Q8oTBLgsY+QVZW4mjvegcXl/EOjlw0irmqAf6BNkGTSDLbsnQBmp93OFEdKc6q3CeOy6Il07GVnF8GCe4ZNCxJQZc9xmBB1WVMcYQtyALRh1/f0XaSbUi5wI4i5mP8K0HMYvAQY9g3b1LYW7I3y+K4HVkcJ4trlfxLfsdvURsQSwOzWUAUKEdlQ4H0vIKLsi6iEXMBSRmqYCmrEh/21mP1DDQgkY5asSR5it+HpOie/oxM7U7rkL3fEWbqUYC/vWrDoYOHU6mJlRqrLvH7sgcnvx3/J3D1piXM6P/esFA9iE92z+piuY2wKaKqMaDoNpkBzA=='
      resources:
        limits:
          cpu: 2
          memory: 20Gi
        requests:
          cpu: 2
          memory: 20Gi
