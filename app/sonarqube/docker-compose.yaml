version: "3"
services:
  sonarqube:
    container_name: sonarqube
    image: sonarqube:8.9.10-community
    restart: always
    environment:
      - SONARQUBE_JDBC_USERNAME=root
      - SONARQUBE_JDBC_PASSWORD=Gotapd8!
      - SONARQUBE_JDBC_URL=*********************************************
    ports:
      - "29092:9092"
      - "29000:9000"
    deploy:
      resources:
        limits:
          cpus: "4"
          memory: 8G
        reservations:
          cpus: "4"
          memory: 8G
    volumes:
      - ./sonar.properties:/opt/sonarqube/conf/sonar.properties
      - ./sonarqube-community-branch-plugin-1.8.3.jar:/opt/sonarqube/extensions/plugins/sonarqube-community-branch-plugin-1.8.3.jar