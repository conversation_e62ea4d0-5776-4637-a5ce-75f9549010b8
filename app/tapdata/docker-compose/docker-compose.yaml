version: "3"
services:
  tapdata-dev:
    image: ghcr.io/tapdata/tapdata-enterprise:v3.5.8-65890c7b
    container_name: tapdata-dev
    restart: always
    ports:
      - "3030:3030"
      - "3080:3080"
    environment:
      - MONGODB_CONNECTION_STRING=*************:37017/license?authSource=admin
      - MONGODB_PASSWORD=Gotapd8!
      - MONGODB_USER=root
      - FRONTEND_WORKER_COUNT=1
      - API_WORKER_COUNT=1
    healthcheck:
      test: curl --fail http://localhost:3030/ || exit 1
      interval: 2m
      timeout: 20s
      retries: 3
    volumes:
      - /log/tapdata-dev:/tapdata/apps/logs
      - ./docker-entrypoint.sh:/tapdata/docker-entrypoint.sh
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 8G
        reservations:
          cpus: '4'
          memory: 8G
