#!/bin/bash

# logging functions
daas_log() {
    local type="$1"
    shift
    printf '%s [%s] [Entrypoint]: %s\n' "$(date --rfc-3339=seconds)" "$type" "$*"
}

daas_note() {
    daas_log INFO "$@"
}

daas_warn() {
    daas_log Warn "$@" >&2
}

daas_error() {
    daas_log ERROR "$@" >&2
    exit 1
}

if [[ -z $TAPDATA_WORK_DIR ]]; then
    TAPDATA_WORK_DIR=/tapdata/apps
fi

# 检查是否是其它脚本引用
_is_sourced() {
    # https://unix.stackexchange.com/a/215279
    [ "${#FUNCNAME[@]}" -ge 2 ] \
        && [ "${FUNCNAME[0]}" = '_is_sourced' ] \
        && [ "${FUNCNAME[1]}" = 'source' ]
}

# usage: file_env VAR [DEFAULT]
file_env() {
    local var="$1"
    local fileVar="${var}_FILE"
    local def="${2:-}"
    if [ "${!var:-}" ] && [ "${!fileVar:-}" ]; then
        daas_error "Both $var and $fileVar are set (but are exclusive)"
    fi
    local val="$def"
    if [ "${!var:-}" ]; then
        val="${!var}"
    elif [ "${!fileVar:-}" ]; then
        val="$(< "${!fileVar}")"
    fi
    export "$var"="$val"
    unset "$fileVar"
}

docker_setup_env() {
    file_env 'MONGODB_USER'
    file_env 'MONGODB_PASSWORD'
    file_env 'MONGODB_CONNECTION_STRING'
    file_env 'BACKENDURL'
    file_env 'MODULE'
}

docker_tapdata_start() {
    daas_note "Waiting for tapdata startup"
    if [ -z "$MODULE" ]; then
        rm -rf ~/.local/*
        chmod +x /tapdata/apps/tapdata
        /tapdata/apps/tapdata start --workDir $TAPDATA_WORK_DIR
    else
        rm -rf ~/.local/*
        /tapdata/apps/tapdata start $MODULE --workDir $TAPDATA_WORK_DIR
    fi
}

set_license() {
    echo "get license..."
    echo "login..."
    curl -v -XPOST -H "Content-Type:application/json" http://192.168.1.184:18080/ldap/login -d '{"password": "Gotapd8!", "uid": "license-temp"}'
    if [[ $? -ne 0 ]]; then
        echo "login failed"
        exit 1
    fi
    resp_json=$(curl -sb -XPOST -H "Content-Type:application/json" -H "uid:license-temp" http://192.168.1.184:18080/license -d '{"customer": "test", "reason": "本地测试", "sid": "", "valid_days": 14, "version": "2.7"}')
    apt install -y jq
    path=$TAPDATA_WORK_DIR
    mkdir -p $path
    mkdir -p ~/.tapdata
    echo $resp_json | jq -r .data.content > $path/license.txt
    echo $resp_json | jq -r .data.content > ~/.tapdata/license.txt
    if [[ -f $path/license.txt && -f ~/.tapdata/license.txt ]]; then
        ls -al $path/license.txt
        ls -al ~/.tapdata/license.txt
        echo "set license file success."
    else
        echo "set license file failed."
    fi
}

docker_setup_tapdata() {
    if [ -n "$MONGODB_USER" ]; then
        sed -ri "s#username:.*#username: $MONGODB_USER#" /tapdata/apps/application.yml
    fi

    if [ -z "$MONGODB_CONNECTION_STRING" ]; then
        daas_error "MONGODB_CONNECTION_STRING not set.\n Did you forget to add -e MONGODB_CONNECTION_STRING=... ?"
    else
        sed -ri "s#(mongoConnectionString:.*').*(')#\1$MONGODB_CONNECTION_STRING\2#" /tapdata/apps/application.yml
    fi

    if [ -n "$BACKENDURL" ]; then
        sed -ri "s#(backendUrl:.*').*(')#\1$BACKENDURL\2#" /tapdata/apps/application.yml
    fi
    cp /tapdata/apps/application.yml $TAPDATA_WORK_DIR/application.yml

    if [ -n "$MONGODB_PASSWORD" ]; then
        /tapdata/apps/tapdata status --workDir $TAPDATA_WORK_DIR
        /tapdata/apps/tapdata resetpassword $MONGODB_PASSWORD
    fi
}

unzip_files() {
    tar xzvf /tapdata/apps/connectors/dist.tar.gz -C /tapdata/apps/
    cd /tapdata/apps/connectors/ && tar xzvf dist.tar.gz
    tar xzvf /tapdata/apps/components/apiserver.tar.gz -C /tapdata/apps/
}

_main() {
    # cp /tmp/conf/application.yml /tapdata/apps/application.yml
    echo "export JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF8" >> /etc/profile && . /etc/profile
    unzip_files
    set_license

    daas_note "Entrypoint script for tapmanager Server started."

    # Load various environment variables
    docker_setup_env "$@"

    db=$(echo $MONGODB_CONNECTION_STRING | awk -F '/' '{print $2}' | awk -F '?' '{print $1}')
    TAPDATA_MONGO_URI="mongodb://root:$MONGODB_PASSWORD@$MONGODB_CONNECTION_STRING"
    echo "restore data to db: $db, to uri: ${TAPDATA_MONGO_URI}"

    docker_setup_tapdata
    if [[ $TAPDATA_WORK_DIR != "" && -d /tapdata/apps/components/webroot/ ]]; then
        mkdir -p $TAPDATA_WORK_DIR/components/webroot/
        cp -r /tapdata/apps/components/webroot/* $TAPDATA_WORK_DIR/components/webroot/
    fi

    daas_note "Starting tapdata server"
    docker_tapdata_start
    daas_note "tapdata server started."

    exec "$@"
}

# 如果是其它脚本引用，则不执行操作
if ! _is_sourced; then
    _main "$@"
fi

while [[ 1 ]]; do
    sleep 10
done