apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    app.kubernetes.io/managed-by: Helm
  name: tapdata-vtest
  namespace: dev
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: standard-rwo
  volumeMode: Filesystem
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: "1"
    meta.helm.sh/release-name: tapdata-vtest
    meta.helm.sh/release-namespace: dev
  labels:
    app.kubernetes.io/managed-by: Helm
    auto_update: "false"
    git.branch: release-v3.3
    io.kompose.service: tapdata-vtest-tapdaas
  name: tapdata-vtest-tapdaas
  namespace: dev
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      io.kompose.service: tapdata-vtest-tapdaas
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        io.kompose.service: tapdata-vtest-tapdaas
    spec:
      containers:
      - env:
        - name: MONGODB_CONNECTION_STRING
          value: 10.35.131.118:30154/tapdata-vtest?authSource=admin
        - name: MONGODB_PASSWORD
          value: Gotapd8!
        - name: MONGODB_USER
          value: root
        - name: FRONTEND_WORKER_COUNT
          value: "1"
        - name: API_WORKER_COUNT
          value: "1"
        image: asia-docker.pkg.dev/crypto-reality-377106/tapdata/tapdata-enterprise:v3.5.4-65376774
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 30
          httpGet:
            path: /
            port: 3030
            scheme: HTTP
          initialDelaySeconds: 120
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        name: tapdata-vtest-tapdaas
        ports:
        - containerPort: 3080
          protocol: TCP
        - containerPort: 3030
          protocol: TCP
        readinessProbe:
          failureThreshold: 30
          httpGet:
            path: /
            port: 3030
            scheme: HTTP
          initialDelaySeconds: 120
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        resources:
          limits:
            cpu: "4"
            ephemeral-storage: 10Gi
            memory: 8Gi
          requests:
            cpu: "4"
            ephemeral-storage: 10Gi
            memory: 8Gi
        securityContext:
          capabilities:
            drop:
            - NET_RAW
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /tapdata/docker-entrypoint.sh
          name: volume-f8upz6
          readOnly: true
          subPath: docker-entrypoint.sh
        - mountPath: /root/
          name: tapdata-vtest
      dnsPolicy: ClusterFirst
      imagePullSecrets:
      - name: coding-repo
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        seccompProfile:
          type: RuntimeDefault
      terminationGracePeriodSeconds: 30
      tolerations:
      - effect: NoSchedule
        key: kubernetes.io/arch
        operator: Equal
        value: amd64
      volumes:
      - configMap:
          defaultMode: 493
          items:
          - key: entry_point
            path: docker-entrypoint.sh
          - key: auto_discovery
            path: auto-discovery.sh
          name: tapdata-mount-entrypoint
        name: volume-f8upz6
      - name: kube-api-access-cnqsk
        projected:
          defaultMode: 420
          sources:
          - serviceAccountToken:
              expirationSeconds: 3607
              path: token
          - configMap:
              items:
              - key: ca.crt
                path: ca.crt
              name: kube-root-ca.crt
          - downwardAPI:
              items:
              - fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
                path: namespace
      - name: tapdata-vtest
        persistentVolumeClaim:
          claimName: tapdata-vtest
---
apiVersion: v1
kind: Service
metadata:
  name: tapdata-vtest
  namespace: dev
  annotations:
    cloud.google.com/l4-rbs: "enabled"
  labels:
    app: tapdata-vtest
spec:
  type: LoadBalancer
  externalTrafficPolicy: Cluster
  ports:
    - name: api
      port: 30735
      nodePort: 30735
      protocol: TCP
      targetPort: 3080
    - name: web
      port: 30373
      nodePort: 30373
      protocol: TCP
      targetPort: 3030
    - name: websocket
      port: 31371
      nodePort: 31371
      protocol: TCP
      targetPort: 8246
  selector:
    io.kompose.service: tapdata-vtest-tapdaas