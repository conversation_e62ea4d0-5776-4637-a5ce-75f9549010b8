FROM mongo:4.2.20
RUN mkdir -p /tapdata/data /tapdata/apps/manager /tapdata/apps/iengine
RUN apt-get update -y && apt-get install software-properties-common -y && add-apt-repository ppa:deadsnakes/ppa -y && apt-get install python3-pip -y && apt-get install locales -y && apt-get install curl -y && apt-get install default-jre -y && pip3 install --upgrade pip && pip3 install --upgrade distlib && pip3 install --upgrade setuptools && locale-gen en_US.UTF-8 && echo "export LC_ALL=en_US.UTF-8" >> /etc/profile && cd /usr/bin && rm -rf bsondump mongodump mongoexport mongofiles mongoimport mongos mongostat mongotop
COPY requirements.txt /
RUN pip3 install -r /requirements.txt
RUN mkdir -p /root/.m2/repository
RUN echo "export JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF8" >> /etc/profile
RUN apt-get install -y python3.7 lsof wget && \
    ALLURE_VERSION=2.13.9 && \
    wget -qO /tmp/allure-"$ALLURE_VERSION".tgz https://github.com/allure-framework/allure2/releases/download/"$ALLURE_VERSION"/allure-"$ALLURE_VERSION".tgz && \
    tar -xf /tmp/allure-"$ALLURE_VERSION".tgz --directory=/opt/ && \
    ln -s /opt/allure-"$ALLURE_VERSION"/bin/allure /usr/bin/allure && \
    update-alternatives --install /usr/bin/python python /usr/bin/python3.7 1 && \
    update-alternatives --set python /usr/bin/python3.7 && \
    curl -s https://bootstrap.pypa.io/get-pip.py -o get-pip.py && \
    python get-pip.py --force-reinstall && \
    rm get-pip.py
RUN apt-get install python3.7-dev -y
