---
- name: Service Defined
  hosts: default
  user: cicd
  become: true
  vars:
    ansible_ssh_private_key_file: id_rsa
  tasks:
    - name: Sync Node_Exporter Service
      copy:
        src: "{{ item.src }}"
        dest: "{{ item.dest }}"
        owner: root
        group: root
        mode: '{{ item.mode }}'
      with_items:
        - src: files/usr/local/node_exporter/node_exporter
          dest: /usr/local/node_exporter/node_exporter
          mode: '0755'
        - src: files/etc/systemd/system/node_exporter.service
          dest: /etc/systemd/system/node_exporter.service
          mode: '0644'
    - name: Node_Exporter Service Defined
      systemd:
        name: node_exporter
        enabled: yes
        state: started
    - name: Open Port 9100
      firewalld:
        port: 9100/tcp
        state: enabled
        permanent: true
        immediate: true
    - name: Reload Firewalld
      systemd:
        name: firewalld
        state: reloaded
