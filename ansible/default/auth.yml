---
- name: Authenticated Users
  hosts: default
  user: cicd
  become: true
  vars:
    ansible_ssh_private_key_file: id_rsa
  tasks:
    - name: Group Defined
      group:
        name: "{{ item }}"
        state: present
      with_items:
        - admin
        - others
    - name: User Defined in Admin Group
      user:
        name: "{{ item }}"
        shell: /bin/bash
        home: "/home/<USER>"
        append: yes
        groups: admin
        createhome: yes
        state: present
      with_items:
        - cicd
        - lg
        - gaojerry
    - name: User Defined in Others Group
      user:
        name: "{{ item }}"
        shell: /bin/bash
        home: "/home/<USER>"
        append: yes
        groups: others
        createhome: yes
        state: present
      with_items:
        - jackin
        - umer
        - wim
        - jason
        - lemon
        - steven
        - will
        - zhoupeng
        - kiki
        - harsen
    - name: Directories Defined
      ansible.builtin.file:
        path: "{{ item.path }}"
        state: directory
        owner: "{{ item.owner }}"
        group: "{{ item.group }}"
        mode: "{{ item.mode }}"
      with_items:
        - path: /root/.ssh
          owner: root
          group: root
          mode: '0700'
        - path: /home/<USER>/.ssh
          owner: cicd
          group: cicd
          mode: '0700'
        - path: /home/<USER>/.ssh
          owner: jackin
          group: jackin
          mode: '0700'
        - path: /home/<USER>/.ssh
          owner: umer
          group: umer
          mode: '0700'
        - path: /home/<USER>/.ssh
          owner: wim
          group: wim
          mode: '0700'
        - path: /home/<USER>/.ssh
          owner: jason
          group: jason
          mode: '0700'
        - path: /home/<USER>/.ssh
          owner: lemon
          group: lemon
          mode: '0700'
        - path: /home/<USER>/.ssh
          owner: lg
          group: lg
          mode: '0700'
        - path: /home/<USER>/.ssh
          owner: steven
          group: steven
          mode: '0700'
        - path: /home/<USER>/.ssh
          owner: will
          group: will
          mode: '0700'
        - path: /home/<USER>/.ssh
          owner: zhoupeng
          group: zhoupeng
          mode: '0700'
        - path: /home/<USER>/.ssh
          owner: kiki
          group: kiki
          mode: '0700'
        - path: /home/<USER>/.ssh
          owner: harsen
          group: harsen
          mode: '0700'
        - path: /home/<USER>/.ssh
          owner: gaojerry
          group: gaojerry
          mode: '0700'
    - name: Sync files
      copy:
        src: "{{ item.src }}"
        dest: "{{ item.dest }}"
        owner: "{{ item.owner }}"
        group: "{{ item.group }}"
        mode: "{{ item.mode }}"
      with_items:
        - src: files/root/.ssh/authorized_keys
          dest: /root/.ssh/authorized_keys
          owner: root
          group: root
          mode: '0600'
        - src: files/home/<USER>/.ssh/authorized_keys
          dest: /home/<USER>/.ssh/authorized_keys
          owner: cicd
          group: cicd
          mode: '0600'
        - src: files/home/<USER>/.ssh/authorized_keys
          dest: /home/<USER>/.ssh/authorized_keys
          owner: jackin
          group: jackin
          mode: '0600'
        - src: files/home/<USER>/.ssh/authorized_keys
          dest: /home/<USER>/.ssh/authorized_keys
          owner: umer
          group: umer
          mode: '0600'
        - src: files/home/<USER>/.ssh/authorized_keys
          dest: /home/<USER>/.ssh/authorized_keys
          owner: wim
          group: wim
          mode: '0600'
        - src: files/home/<USER>/.ssh/authorized_keys
          dest: /home/<USER>/.ssh/authorized_keys
          owner: jason
          group: jason
          mode: '0600'
        - src: files/home/<USER>/.ssh/authorized_keys
          dest: /home/<USER>/.ssh/authorized_keys
          owner: lemon
          group: lemon
          mode: '0600'
        - src: files/home/<USER>/.ssh/authorized_keys
          dest: /home/<USER>/.ssh/authorized_keys
          owner: kiki
          group: kiki
          mode: '0600'
        - src: files/home/<USER>/.ssh/authorized_keys
          dest: /home/<USER>/.ssh/authorized_keys
          owner: lg
          group: lg
          mode: '0600'
        - src: files/home/<USER>/.ssh/authorized_keys
          dest: /home/<USER>/.ssh/authorized_keys
          owner: steven
          group: steven
          mode: '0600'
        - src: files/home/<USER>/.ssh/authorized_keys
          dest: /home/<USER>/.ssh/authorized_keys
          owner: harsen
          group: harsen
          mode: '0600'
        - src: files/home/<USER>/.ssh/authorized_keys
          dest: /home/<USER>/.ssh/authorized_keys
          owner: will
          group: will
          mode: '0600'
        - src: files/home/<USER>/.ssh/authorized_keys
          dest: /home/<USER>/.ssh/authorized_keys
          owner: zhoupeng
          group: zhoupeng
          mode: '0600'
        - src: files/home/<USER>/.ssh/authorized_keys
          dest: /home/<USER>/.ssh/authorized_keys
          owner: gaojerry
          group: gaojerry
          mode: '0600'
        - src: files/etc/sudoers
          dest: /etc/sudoers
          owner: root
          group: root
          mode: '0440'
        - src: files/etc/ssh/sshd_config
          dest: /etc/ssh/sshd_config
          owner: root
          group: root
          mode: '0600'
        - src: files/etc/docker/daemon.json
          dest: /etc/docker/daemon.json
          owner: root
          group: root
          mode: '0644'
    - name: Restart sshd using Systemd
      systemd:
        name: sshd
        state: restarted
