---
- name: Create Directories
  hosts: default
  user: cicd
  become: true
  vars:
    ansible_ssh_private_key_file: id_rsa
  tasks:
    - name: Create directory
      file:
        path: '{{ item.path }}'
        state: directory
        owner: '{{ item.owner }}'
        group: '{{ item.group }}'
        mode: '{{ item.mode }}'
      with_items:
        - path: /app
          mode: '0755'
          owner: cicd
          group: cicd
        - path: /data
          mode: '0777'
          owner: cicd
          group: cicd
        - path: /log
          mode: '0755'
          owner: cicd
          group: cicd
        - path: /usr/local/node_exporter
          mode: '0755'
          owner: root
          group: root
