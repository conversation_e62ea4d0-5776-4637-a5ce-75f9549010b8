---
- name: Install Packages
  hosts: default
  user: cicd
  become: true
  vars:
      ansible_ssh_private_key_file: id_rsa
  tasks:
    # Install required packages
    - name: install yum-utils
      yum: name=yum-utils state=present
    - name: add docker repo
      yum_repository:
        name: docker-ce-stable
        description: Docker CE Stable - $basearch
        baseurl: https://download.docker.com/linux/centos/docker-ce.repo
        gpgcheck: yes
        enabled: yes
        gpgkey: https://download.docker.com/linux/centos/gpg
        state: present
    - name: Install required packages
      yum:
        name: "{{ item }}"
        state: present
        allow_downgrade: yes
      with_items:
        - vim
        - git
        - docker-ce-3:24.0.7-1.el8
        - docker-ce-cli-1:24.0.7-1.el8
        - containerd.io-1.6.26-3.1.el8
        - docker-buildx-plugin-0.11.2-1.el8
        - docker-ce-rootless-extras-24.0.7-1.el8
        - docker-compose-plugin-2.21.0-1.el8
        - pcp-pmda-docker-5.3.1-5.el8
      when: ansible_os_family == "RedHat" and ansible_distribution_major_version == "8"
    # Start and enable service
    - name: Start requiered services
      service:
        name: "{{ item }}"
        state: started
        enabled: yes
      with_items:
        - docker
      when: ansible_os_family == "RedHat" and ansible_distribution_major_version == "8"
