Cmnd_Alias SOFTWARE = /bin/rpm, /usr/bin/up2date, /usr/bin/yum
Cmnd_Alias DOCKER   = /bin/docker
Cmnd_Alias YUM      = /bin/yum
Cmnd_Alias RM       = /bin/rm

Defaults   !visiblepw
Defaults    always_set_home
Defaults    match_group_by_gid
Defaults    always_query_group_plugin

Defaults    env_reset
Defaults    env_keep =  "COLORS DISPLAY HOSTNAME HISTSIZE KDEDIR LS_COLORS"
Defaults    env_keep += "MAIL PS1 PS2 QTDIR USERNAME LANG LC_ADDRESS LC_CTYPE"
Defaults    env_keep += "LC_COLLATE LC_IDENTIFICATION LC_MEASUREMENT LC_MESSAGES"
Defaults    env_keep += "LC_MONETARY LC_NAME LC_NUMERIC LC_PAPER LC_TELEPHONE"
Defaults    env_keep += "LC_TIME LC_ALL LANGUAGE LINGUAS _XKB_CHARSET XAUTHORITY"

Defaults    secure_path = /sbin:/bin:/usr/sbin:/usr/bin

root    ALL=(ALL)       ALL

%wheel  ALL=(ALL)       ALL

%admin  ALL=(ALL)       NOPASSWD: ALL
%others ALL=(ALL)       NOPASSWD: YUM,DOCKER

gaojerry ALL=(ALL)      NOPASSWD: ALL
cicd    ALL=(ALL)       NOPASSWD: ALL
will    ALL=(ALL)       NOPASSWD: YUM,DOCKER,RM
