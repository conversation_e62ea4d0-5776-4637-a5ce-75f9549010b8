---
- name: System Settings
  hosts: default
  user: cicd
  become: true
  vars:
    ansible_ssh_private_key_file: id_rsa
  tasks:
    - name: Set Kelnel Parameters
      sysctl:
        name: "{{ item.name }}"
        value: "{{ item.value }}"
        state: present
        reload: yes
      with_items:
        - name: net.ipv4.tcp_max_tw_buckets
          value: 5000
        - name: net.ipv4.tcp_max_syn_backlog
          value: 1024
        - name: net.core.somaxconn
          value: 327675
        - name: net.core.netdev_max_backlog
          value: 163837
        - name: vm.swappiness
          value: 0
        - name: vm.overcommit_memory
          value: 1
        - name: fs.file-max
          value: 65535
      when: ansible_os_family == "RedHat" and ansible_distribution_major_version == "8"

    - name: Limit Pam Settings
      pam_limits:
        domain: "*"
        limit_type: "{{ item.limit_type }}"
        limit_item: "{{ item.limit_item }}"
        value: "{{ item.value }}"
      with_items:
        - limit_type: "soft"
          limit_item: "nofile"
          value: 655350
        - limit_type: "hard"
          limit_item: "nofile"
          value: 655350

    - name: Start Firewall using Systemd
      systemd:
        name: firewalld
        state: started
        enabled: yes

    - name: Set Profile
      copy:
        src: files/etc/profile
        dest: /etc/profile
        owner: root
        group: root
        mode: 0644
        backup: yes
        force: yes
