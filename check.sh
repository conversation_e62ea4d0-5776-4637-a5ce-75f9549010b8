#!/bin/bash

# Feishu webhook URL (replace with your actual webhook URL)
FEISHU_WEBHOOK="https://open.feishu.cn/open-apis/bot/v2/hook/971b05a7-d34a-4f03-81d3-3d17e2e21c47"

# Function to send Feishu alert
send_feishu_alert() {
    local container_id=$1
    local container_name=$2
    local status=$3
    local message="🚨 Docker Container Alert\nContainer ID: ${container_id}\nContainer Name: ${container_name}\nStatus: ${status}\nTime: $(date '+%Y-%m-%d %H:%M:%S')"

    curl -X POST -H "Content-Type: application/json" \
         -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"${message}\"}}" \
         "${FEISHU_WEBHOOK}"
}

# Check Docker containers
check_containers() {
    # Get all containers with github-action-runner in their image name
    docker ps -a --format "{{.ID}}\t{{.Names}}\t{{.Status}}" | grep "github-action-runner" | while read -r line; do
        container_id=$(echo "$line" | awk '{print $1}')
        container_name=$(echo "$line" | awk '{print $2}')
        status=$(echo "$line" | awk '{print $3}')

        # Check if container is not in 'Up' state
        if [[ "$status" != "Up" ]]; then
            echo "Alert: Container ${container_name} (${container_id}) is in ${status} state"
            send_feishu_alert "$container_id" "$container_name" "$status"
        fi
    done
}

check_containers